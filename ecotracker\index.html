<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoTracker - Suiv<PERSON> Empre<PERSON>e <PERSON>e</title>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2d5016;
            font-size: 2.8rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #4a5568;
            font-size: 1.2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .stat-icon.carbon { color: #e53e3e; }
        .stat-icon.transport { color: #3182ce; }
        .stat-icon.energy { color: #d69e2e; }
        .stat-icon.waste { color: #38a169; }

        .stat-number {
            font-size: 2.2rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .activity-form {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #4a5568;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #38a169, #2f855a);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(56, 161, 105, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
        }

        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #38a169;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: #edf2f7;
            transform: translateX(5px);
        }

        .activity-info h4 {
            color: #2d3748;
            margin-bottom: 5px;
        }

        .activity-info p {
            color: #718096;
            font-size: 0.9rem;
        }

        .activity-carbon {
            font-weight: bold;
            color: #e53e3e;
            font-size: 1.1rem;
        }

        .progress-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 25px;
            margin: 15px 0;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill.good {
            background: linear-gradient(90deg, #38a169, #2f855a);
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #d69e2e, #b7791f);
        }

        .progress-fill.danger {
            background: linear-gradient(90deg, #e53e3e, #c53030);
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .tips-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .tip-card {
            background: #f0fff4;
            border: 2px solid #38a169;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .tip-card:hover {
            background: #e6fffa;
            transform: scale(1.02);
        }

        .tip-icon {
            font-size: 2rem;
            color: #38a169;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2.2rem;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <header class="header">
                <h1><i class="fas fa-leaf"></i> EcoTracker</h1>
                <p>Suivez et réduisez votre empreinte carbone quotidienne</p>
            </header>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon carbon">
                        <i class="fas fa-smog"></i>
                    </div>
                    <div class="stat-number" id="totalCarbon">0.0</div>
                    <div class="stat-label">kg CO₂ ce mois</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon transport">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="stat-number" id="transportCarbon">0.0</div>
                    <div class="stat-label">Transport</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon energy">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="stat-number" id="energyCarbon">0.0</div>
                    <div class="stat-label">Énergie</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon waste">
                        <i class="fas fa-trash"></i>
                    </div>
                    <div class="stat-number" id="wasteCarbon">0.0</div>
                    <div class="stat-label">Déchets</div>
                </div>
            </div>

            <div class="main-content">
                <div class="section">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i>
                        Ajouter une activité
                    </h2>

                    <form class="activity-form" id="activityForm">
                        <div class="form-group">
                            <label>Type d'activité</label>
                            <select id="activityType" required>
                                <option value="">Sélectionner...</option>
                                <option value="voiture">🚗 Voiture</option>
                                <option value="avion">✈️ Avion</option>
                                <option value="train">🚆 Train</option>
                                <option value="bus">🚌 Bus</option>
                                <option value="electricite">⚡ Électricité</option>
                                <option value="gaz">🔥 Gaz</option>
                                <option value="dechets">🗑️ Déchets</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Quantité (km, kWh, kg)</label>
                            <input type="number" id="activityQuantity" required min="0" step="0.1">
                        </div>

                        <div class="form-group">
                            <label>Description (optionnel)</label>
                            <input type="text" id="activityDescription" placeholder="Ex: Trajet domicile-travail">
                        </div>

                        <button type="submit" class="btn">
                            <i class="fas fa-plus"></i> Ajouter
                        </button>
                    </form>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <i class="fas fa-list"></i>
                        Activités récentes
                    </h2>

                    <div class="activity-list" id="activityList">
                        <div style="text-align: center; color: #718096; padding: 20px;">
                            Aucune activité enregistrée
                        </div>
                    </div>
                </div>
            </div>

            <div class="progress-section">
                <h2 class="section-title">
                    <i class="fas fa-target"></i>
                    Objectif mensuel: <span id="monthlyGoal">2000</span> kg CO₂
                </h2>

                <div class="progress-bar">
                    <div class="progress-fill good" id="progressFill" style="width: 0%">
                        <div class="progress-text" id="progressText">0%</div>
                    </div>
                </div>

                <p style="text-align: center; margin-top: 10px; color: #4a5568;" id="remainingText">
                    Reste: 2000.0 kg CO₂
                </p>
            </div>

            <div class="tips-section">
                <h2 class="section-title">
                    <i class="fas fa-lightbulb"></i>
                    Conseils éco-responsables
                </h2>

                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon"><i class="fas fa-bicycle"></i></div>
                        <h4>Transport doux</h4>
                        <p>Privilégiez le vélo ou la marche pour les courts trajets</p>
                    </div>

                    <div class="tip-card">
                        <div class="tip-icon"><i class="fas fa-recycle"></i></div>
                        <h4>Recyclage</h4>
                        <p>Triez vos déchets et réduisez votre consommation</p>
                    </div>

                    <div class="tip-card">
                        <div class="tip-icon"><i class="fas fa-home"></i></div>
                        <h4>Économies d'énergie</h4>
                        <p>Éteignez les appareils en veille et isolez votre logement</p>
                    </div>

                    <div class="tip-card">
                        <div class="tip-icon"><i class="fas fa-seedling"></i></div>
                        <h4>Consommation locale</h4>
                        <p>Achetez local et de saison pour réduire l'impact transport</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Service EcoTracker (style Angular)
        class EcoService {
            constructor() {
                this.activities = [];
                this.stats = {
                    totalCarbon: 0,
                    transport: 0,
                    energy: 0,
                    waste: 0
                };
                this.monthlyGoal = 2000;
            }

            addActivity(activity) {
                const carbonFootprint = this.calculateCarbon(activity);
                const newActivity = {
                    ...activity,
                    carbon: carbonFootprint,
                    date: new Date(),
                    id: Date.now()
                };

                this.activities.unshift(newActivity);
                this.updateStats();
                return newActivity;
            }

            removeActivity(id) {
                this.activities = this.activities.filter(a => a.id !== id);
                this.updateStats();
            }

            calculateCarbon(activity) {
                const factors = {
                    'voiture': 0.2,
                    'avion': 0.25,
                    'train': 0.04,
                    'bus': 0.08,
                    'electricite': 0.5,
                    'gaz': 2.3,
                    'dechets': 0.5
                };

                return (activity.quantity * (factors[activity.type] || 0.1)).toFixed(2);
            }

            updateStats() {
                this.stats.totalCarbon = this.activities.reduce((sum, a) => sum + parseFloat(a.carbon), 0);
                this.stats.transport = this.activities
                    .filter(a => ['voiture', 'avion', 'train', 'bus'].includes(a.type))
                    .reduce((sum, a) => sum + parseFloat(a.carbon), 0);
                this.stats.energy = this.activities
                    .filter(a => ['electricite', 'gaz'].includes(a.type))
                    .reduce((sum, a) => sum + parseFloat(a.carbon), 0);
                this.stats.waste = this.activities
                    .filter(a => a.type === 'dechets')
                    .reduce((sum, a) => sum + parseFloat(a.carbon), 0);
            }

            getProgressPercentage() {
                return Math.min(100, (this.stats.totalCarbon / this.monthlyGoal) * 100);
            }

            getProgressClass() {
                const percentage = this.getProgressPercentage();
                if (percentage < 50) return 'good';
                if (percentage < 80) return 'warning';
                return 'danger';
            }
        }

        // Application Controller (style Angular)
        class EcoController {
            constructor() {
                this.ecoService = new EcoService();
                this.initializeApp();
            }

            initializeApp() {
                this.bindEvents();
                this.updateUI();
            }

            bindEvents() {
                const form = document.getElementById('activityForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.addActivity();
                });
            }

            addActivity() {
                const type = document.getElementById('activityType').value;
                const quantity = parseFloat(document.getElementById('activityQuantity').value);
                const description = document.getElementById('activityDescription').value;

                if (type && quantity > 0) {
                    const activity = { type, quantity, description };
                    this.ecoService.addActivity(activity);
                    this.updateUI();
                    this.resetForm();
                    this.showNotification('✅ Activité ajoutée avec succès !');
                }
            }

            removeActivity(id) {
                this.ecoService.removeActivity(id);
                this.updateUI();
                this.showNotification('🗑️ Activité supprimée');
            }

            resetForm() {
                document.getElementById('activityType').value = '';
                document.getElementById('activityQuantity').value = '';
                document.getElementById('activityDescription').value = '';
            }

            updateUI() {
                this.updateStats();
                this.updateActivityList();
                this.updateProgress();
            }

            updateStats() {
                document.getElementById('totalCarbon').textContent = this.ecoService.stats.totalCarbon.toFixed(1);
                document.getElementById('transportCarbon').textContent = this.ecoService.stats.transport.toFixed(1);
                document.getElementById('energyCarbon').textContent = this.ecoService.stats.energy.toFixed(1);
                document.getElementById('wasteCarbon').textContent = this.ecoService.stats.waste.toFixed(1);
            }

            updateActivityList() {
                const listElement = document.getElementById('activityList');

                if (this.ecoService.activities.length === 0) {
                    listElement.innerHTML = '<div style="text-align: center; color: #718096; padding: 20px;">Aucune activité enregistrée</div>';
                    return;
                }

                listElement.innerHTML = this.ecoService.activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-info">
                            <h4>${this.getActivityLabel(activity.type)}</h4>
                            <p>${activity.description || 'Quantité: ' + activity.quantity}</p>
                            <small>${activity.date.toLocaleString('fr-FR')}</small>
                        </div>
                        <div>
                            <div class="activity-carbon">${activity.carbon} kg CO₂</div>
                            <button class="btn btn-danger" onclick="app.removeActivity(${activity.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            updateProgress() {
                const percentage = this.ecoService.getProgressPercentage();
                const progressClass = this.ecoService.getProgressClass();
                const remaining = (this.ecoService.monthlyGoal - this.ecoService.stats.totalCarbon).toFixed(1);

                const progressFill = document.getElementById('progressFill');
                progressFill.style.width = percentage + '%';
                progressFill.className = `progress-fill ${progressClass}`;

                document.getElementById('progressText').textContent = percentage.toFixed(0) + '%';
                document.getElementById('remainingText').textContent = `Reste: ${remaining} kg CO₂`;
            }

            getActivityLabel(type) {
                const labels = {
                    'voiture': '🚗 Voiture',
                    'avion': '✈️ Avion',
                    'train': '🚆 Train',
                    'bus': '🚌 Bus',
                    'electricite': '⚡ Électricité',
                    'gaz': '🔥 Gaz',
                    'dechets': '🗑️ Déchets'
                };
                return labels[type] || type;
            }

            showNotification(message) {
                // Créer une notification temporaire
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #38a169, #2f855a);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 10px;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    animation: slideIn 0.5s ease;
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
        }

        // Initialisation de l'application
        let app;
        document.addEventListener('DOMContentLoaded', () => {
            app = new EcoController();

            // Ajouter quelques données de démonstration
            setTimeout(() => {
                app.ecoService.addActivity({ type: 'voiture', quantity: 25, description: 'Trajet domicile-travail' });
                app.ecoService.addActivity({ type: 'electricite', quantity: 150, description: 'Consommation mensuelle' });
                app.updateUI();
            }, 1000);
        });
    </script>
</body>
</html>
