<?php
// Mini Task Manager - Laravel Style Structure
session_start();

// Configuration
define('BASE_URL', 'http://localhost');
define('DB_HOST', 'localhost');
define('DB_NAME', 'task_manager');
define('DB_USER', 'root');
define('DB_PASS', '');

// Database Connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    // Fallback to SQLite for demo
    $pdo = new PDO("sqlite:tasks.db");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        status VARCHAR(50) DEFAULT 'pending',
        priority VARCHAR(20) DEFAULT 'medium',
        due_date DATE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");
}

// Router
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

// Routes
switch ($path) {
    case '/':
    case '/tasks':
        if ($method === 'GET') {
            showTasks($pdo);
        } elseif ($method === 'POST') {
            createTask($pdo);
        }
        break;
    case '/tasks/create':
        showCreateForm();
        break;
    case (preg_match('/\/tasks\/(\d+)\/edit/', $path, $matches) ? true : false):
        $id = $matches[1];
        if ($method === 'GET') {
            showEditForm($pdo, $id);
        } elseif ($method === 'POST') {
            updateTask($pdo, $id);
        }
        break;
    case (preg_match('/\/tasks\/(\d+)\/delete/', $path, $matches) ? true : false):
        $id = $matches[1];
        deleteTask($pdo, $id);
        break;
    default:
        http_response_code(404);
        echo "Page not found";
        break;
}

// Controllers
function showTasks($pdo) {
    $filter = $_GET['status'] ?? 'all';
    $search = $_GET['search'] ?? '';
    
    $sql = "SELECT * FROM tasks WHERE 1=1";
    $params = [];
    
    if ($filter !== 'all') {
        $sql .= " AND status = ?";
        $params[] = $filter;
    }
    
    if ($search) {
        $sql .= " AND (title LIKE ? OR description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Statistics
    $stats = getStats($pdo);
    
    include 'views/tasks/index.php';
}

function createTask($pdo) {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $priority = $_POST['priority'] ?? 'medium';
    $due_date = $_POST['due_date'] ?? null;
    
    if ($title) {
        $sql = "INSERT INTO tasks (title, description, priority, due_date) VALUES (?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$title, $description, $priority, $due_date]);
        
        $_SESSION['success'] = 'Tâche créée avec succès!';
    }
    
    header('Location: /');
    exit;
}

function showCreateForm() {
    include 'views/tasks/create.php';
}

function showEditForm($pdo, $id) {
    $stmt = $pdo->prepare("SELECT * FROM tasks WHERE id = ?");
    $stmt->execute([$id]);
    $task = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$task) {
        header('Location: /');
        exit;
    }
    
    include 'views/tasks/edit.php';
}

function updateTask($pdo, $id) {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $status = $_POST['status'] ?? 'pending';
    $priority = $_POST['priority'] ?? 'medium';
    $due_date = $_POST['due_date'] ?? null;
    
    if ($title) {
        $sql = "UPDATE tasks SET title = ?, description = ?, status = ?, priority = ?, due_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$title, $description, $status, $priority, $due_date, $id]);
        
        $_SESSION['success'] = 'Tâche mise à jour avec succès!';
    }
    
    header('Location: /');
    exit;
}

function deleteTask($pdo, $id) {
    $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
    $stmt->execute([$id]);
    
    $_SESSION['success'] = 'Tâche supprimée avec succès!';
    header('Location: /');
    exit;
}

function getStats($pdo) {
    $stats = [];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM tasks");
    $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM tasks WHERE status = 'pending'");
    $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['pending'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as completed FROM tasks WHERE status = 'completed'");
    $stats['completed'] = $stmt->fetch(PDO::FETCH_ASSOC)['completed'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as in_progress FROM tasks WHERE status = 'in_progress'");
    $stats['in_progress'] = $stmt->fetch(PDO::FETCH_ASSOC)['in_progress'];
    
    return $stats;
}

// Helper Functions
function asset($path) {
    return BASE_URL . '/assets/' . $path;
}

function route($name, $params = []) {
    $routes = [
        'tasks.index' => '/',
        'tasks.create' => '/tasks/create',
        'tasks.edit' => '/tasks/{id}/edit',
        'tasks.delete' => '/tasks/{id}/delete'
    ];
    
    $url = $routes[$name] ?? '/';
    
    foreach ($params as $key => $value) {
        $url = str_replace('{' . $key . '}', $value, $url);
    }
    
    return $url;
}

function old($key, $default = '') {
    return $_POST[$key] ?? $default;
}

function session($key) {
    $value = $_SESSION[$key] ?? null;
    unset($_SESSION[$key]);
    return $value;
}
?>
