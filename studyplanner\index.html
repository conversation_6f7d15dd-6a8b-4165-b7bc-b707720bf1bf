<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StudyPlanner - Planificateur d'Études</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #4c51bf;
            font-size: 2.8rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #4a5568;
            font-size: 1.2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .stat-icon.hours { color: #3182ce; }
        .stat-icon.subjects { color: #38a169; }
        .stat-icon.tasks { color: #d69e2e; }
        .stat-icon.streak { color: #e53e3e; }

        .stat-number {
            font-size: 2.2rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #4a5568;
        }

        .form-group select,
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group select:focus,
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #4c51bf, #667eea);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 81, 191, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #38a169, #2f855a);
        }

        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(56, 161, 105, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);
        }

        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .task-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #4c51bf;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .task-item:hover {
            background: #edf2f7;
            transform: translateX(5px);
        }

        .task-item.completed {
            background: #c6f6d5;
            border-left-color: #38a169;
            opacity: 0.8;
        }

        .task-item.high-priority {
            border-left-color: #e53e3e;
        }

        .task-item.medium-priority {
            border-left-color: #d69e2e;
        }

        .task-info h4 {
            color: #2d3748;
            margin-bottom: 5px;
        }

        .task-info p {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .task-meta {
            display: flex;
            gap: 10px;
            font-size: 0.8rem;
            color: #a0aec0;
        }

        .task-actions {
            display: flex;
            gap: 5px;
        }

        .schedule-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .day-card {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            min-height: 120px;
            transition: all 0.3s ease;
        }

        .day-card:hover {
            background: #edf2f7;
            transform: scale(1.02);
        }

        .day-card.today {
            background: linear-gradient(135deg, #4c51bf, #667eea);
            color: white;
        }

        .day-name {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .day-tasks {
            font-size: 0.8rem;
        }

        .progress-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
        }

        .subject-progress {
            margin-bottom: 20px;
        }

        .subject-name {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .progress-fill.math { background: linear-gradient(90deg, #3182ce, #2c5282); }
        .progress-fill.science { background: linear-gradient(90deg, #38a169, #2f855a); }
        .progress-fill.history { background: linear-gradient(90deg, #d69e2e, #b7791f); }
        .progress-fill.language { background: linear-gradient(90deg, #e53e3e, #c53030); }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .schedule-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .header h1 {
                font-size: 2.2rem;
            }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4c51bf, #667eea);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <header class="header">
                <h1><i class="fas fa-graduation-cap"></i> StudyPlanner</h1>
                <p>Organisez vos études et atteignez vos objectifs académiques</p>
            </header>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon hours">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number" id="totalHours">0</div>
                    <div class="stat-label">Heures d'étude</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon subjects">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-number" id="totalSubjects">0</div>
                    <div class="stat-label">Matières actives</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon tasks">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-number" id="completedTasks">0</div>
                    <div class="stat-label">Tâches terminées</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon streak">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="stat-number" id="studyStreak">0</div>
                    <div class="stat-label">Jours consécutifs</div>
                </div>
            </div>

            <div class="main-content">
                <div class="section">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i>
                        Ajouter une tâche
                    </h2>
                    
                    <form id="taskForm">
                        <div class="form-group">
                            <label>Matière</label>
                            <select id="taskSubject" required>
                                <option value="">Sélectionner...</option>
                                <option value="math">📐 Mathématiques</option>
                                <option value="science">🔬 Sciences</option>
                                <option value="history">📚 Histoire</option>
                                <option value="language">🗣️ Langues</option>
                                <option value="other">📝 Autre</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Titre de la tâche</label>
                            <input type="text" id="taskTitle" required placeholder="Ex: Réviser chapitre 5">
                        </div>

                        <div class="form-group">
                            <label>Description</label>
                            <textarea id="taskDescription" rows="3" placeholder="Détails de la tâche..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>Priorité</label>
                            <select id="taskPriority" required>
                                <option value="low">🟢 Faible</option>
                                <option value="medium">🟡 Moyenne</option>
                                <option value="high">🔴 Élevée</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Date d'échéance</label>
                            <input type="date" id="taskDueDate" required>
                        </div>

                        <div class="form-group">
                            <label>Durée estimée (heures)</label>
                            <input type="number" id="taskDuration" min="0.5" step="0.5" value="1">
                        </div>

                        <button type="submit" class="btn">
                            <i class="fas fa-plus"></i> Ajouter la tâche
                        </button>
                    </form>
                </div>

                <div class="section">
                    <h2 class="section-title">
                        <i class="fas fa-list-check"></i>
                        Tâches à faire
                    </h2>
                    
                    <div class="task-list" id="taskList">
                        <div style="text-align: center; color: #718096; padding: 20px;">
                            Aucune tâche planifiée
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">
                    <i class="fas fa-calendar-week"></i>
                    Planning des 3 prochains jours
                </h2>
                
                <div class="schedule-grid" id="scheduleGrid">
                    <!-- Généré dynamiquement -->
                </div>
            </div>

            <div class="progress-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-line"></i>
                    Progression par matière
                </h2>
                
                <div id="subjectProgress">
                    <!-- Généré dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Service StudyPlanner (style Angular)
        class StudyService {
            constructor() {
                this.tasks = [];
                this.stats = {
                    totalHours: 0,
                    totalSubjects: 0,
                    completedTasks: 0,
                    studyStreak: 5
                };
                this.subjects = {
                    math: { name: 'Mathématiques', progress: 0, hours: 0 },
                    science: { name: 'Sciences', progress: 0, hours: 0 },
                    history: { name: 'Histoire', progress: 0, hours: 0 },
                    language: { name: 'Langues', progress: 0, hours: 0 }
                };
            }

            addTask(task) {
                const newTask = {
                    ...task,
                    id: Date.now(),
                    completed: false,
                    createdAt: new Date()
                };
                
                this.tasks.unshift(newTask);
                this.updateStats();
                return newTask;
            }

            completeTask(id) {
                const task = this.tasks.find(t => t.id === id);
                if (task && !task.completed) {
                    task.completed = true;
                    task.completedAt = new Date();
                    
                    // Ajouter les heures à la matière
                    if (this.subjects[task.subject]) {
                        this.subjects[task.subject].hours += parseFloat(task.duration);
                        this.subjects[task.subject].progress = Math.min(100, this.subjects[task.subject].progress + 10);
                    }
                    
                    this.updateStats();
                }
            }

            removeTask(id) {
                this.tasks = this.tasks.filter(t => t.id !== id);
                this.updateStats();
            }

            updateStats() {
                this.stats.completedTasks = this.tasks.filter(t => t.completed).length;
                this.stats.totalHours = Object.values(this.subjects).reduce((sum, s) => sum + s.hours, 0);
                this.stats.totalSubjects = Object.values(this.subjects).filter(s => s.hours > 0).length;
            }

            getTasksForWeek() {
                const today = new Date();
                const weekTasks = {};

                for (let i = 0; i < 3; i++) {
                    const date = new Date(today);
                    date.setDate(today.getDate() + i);
                    const dateStr = date.toISOString().split('T')[0];

                    weekTasks[dateStr] = this.tasks.filter(task =>
                        task.dueDate === dateStr && !task.completed
                    );
                }

                return weekTasks;
            }
        }

        // Application Controller (style Angular)
        class StudyController {
            constructor() {
                this.studyService = new StudyService();
                this.initializeApp();
            }

            initializeApp() {
                this.bindEvents();
                this.updateUI();
                this.generateWeekSchedule();
                this.addDemoData();
            }

            bindEvents() {
                const form = document.getElementById('taskForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.addTask();
                });

                // Définir la date minimale à aujourd'hui
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('taskDueDate').min = today;
                document.getElementById('taskDueDate').value = today;
            }

            addTask() {
                const task = {
                    subject: document.getElementById('taskSubject').value,
                    title: document.getElementById('taskTitle').value,
                    description: document.getElementById('taskDescription').value,
                    priority: document.getElementById('taskPriority').value,
                    dueDate: document.getElementById('taskDueDate').value,
                    duration: parseFloat(document.getElementById('taskDuration').value)
                };

                if (task.subject && task.title && task.dueDate) {
                    this.studyService.addTask(task);
                    this.updateUI();
                    this.resetForm();
                    this.showNotification('📚 Tâche ajoutée avec succès !');
                }
            }

            completeTask(id) {
                this.studyService.completeTask(id);
                this.updateUI();
                this.showNotification('✅ Tâche terminée ! Bien joué !');
            }

            removeTask(id) {
                this.studyService.removeTask(id);
                this.updateUI();
                this.showNotification('🗑️ Tâche supprimée');
            }

            resetForm() {
                document.getElementById('taskForm').reset();
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('taskDueDate').value = today;
                document.getElementById('taskDuration').value = '1';
            }

            updateUI() {
                this.updateStats();
                this.updateTaskList();
                this.updateSubjectProgress();
                this.generateWeekSchedule();
            }

            updateStats() {
                document.getElementById('totalHours').textContent = this.studyService.stats.totalHours.toFixed(1);
                document.getElementById('totalSubjects').textContent = this.studyService.stats.totalSubjects;
                document.getElementById('completedTasks').textContent = this.studyService.stats.completedTasks;
                document.getElementById('studyStreak').textContent = this.studyService.stats.studyStreak;
            }

            updateTaskList() {
                const listElement = document.getElementById('taskList');
                const pendingTasks = this.studyService.tasks.filter(t => !t.completed);

                if (pendingTasks.length === 0) {
                    listElement.innerHTML = '<div style="text-align: center; color: #718096; padding: 20px;">Aucune tâche planifiée</div>';
                    return;
                }

                listElement.innerHTML = pendingTasks.map(task => `
                    <div class="task-item ${task.priority}-priority">
                        <div class="task-info">
                            <h4>${this.getSubjectLabel(task.subject)} - ${task.title}</h4>
                            <p>${task.description || 'Pas de description'}</p>
                            <div class="task-meta">
                                <span><i class="fas fa-calendar"></i> ${new Date(task.dueDate).toLocaleDateString('fr-FR')}</span>
                                <span><i class="fas fa-clock"></i> ${task.duration}h</span>
                                <span class="priority-${task.priority}">
                                    ${task.priority === 'high' ? '🔴' : task.priority === 'medium' ? '🟡' : '🟢'}
                                    ${task.priority === 'high' ? 'Urgent' : task.priority === 'medium' ? 'Moyen' : 'Faible'}
                                </span>
                            </div>
                        </div>
                        <div class="task-actions">
                            <button class="btn btn-success" onclick="app.completeTask(${task.id})" title="Marquer comme terminé">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-danger" onclick="app.removeTask(${task.id})" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            updateSubjectProgress() {
                const progressElement = document.getElementById('subjectProgress');

                progressElement.innerHTML = Object.entries(this.studyService.subjects).map(([key, subject]) => `
                    <div class="subject-progress">
                        <div class="subject-name">
                            <span>${subject.name}</span>
                            <span>${subject.hours.toFixed(1)}h - ${subject.progress}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill ${key}" style="width: ${subject.progress}%"></div>
                        </div>
                    </div>
                `).join('');
            }

            generateWeekSchedule() {
                const scheduleElement = document.getElementById('scheduleGrid');
                const weekTasks = this.studyService.getTasksForWeek();
                const today = new Date();

                const days = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];

                scheduleElement.innerHTML = '';

                for (let i = 0; i < 3; i++) {
                    const date = new Date(today);
                    date.setDate(today.getDate() + i);
                    const dateStr = date.toISOString().split('T')[0];
                    const dayTasks = weekTasks[dateStr] || [];
                    const isToday = i === 0;

                    const dayCard = document.createElement('div');
                    dayCard.className = `day-card ${isToday ? 'today' : ''}`;
                    dayCard.innerHTML = `
                        <div class="day-name">${days[date.getDay()]}</div>
                        <div style="font-size: 0.9rem; margin-bottom: 10px;">${date.getDate()}/${date.getMonth() + 1}</div>
                        <div class="day-tasks">
                            ${dayTasks.length > 0 ?
                                dayTasks.slice(0, 3).map(task => `
                                    <div style="background: rgba(255,255,255,0.2); padding: 5px; border-radius: 5px; margin: 2px 0; font-size: 0.7rem;">
                                        ${task.title}
                                    </div>
                                `).join('') + (dayTasks.length > 3 ? `<div style="font-size: 0.6rem;">+${dayTasks.length - 3} autres</div>` : '')
                                : '<div style="color: #a0aec0; font-size: 0.7rem;">Libre</div>'
                            }
                        </div>
                    `;
                    scheduleElement.appendChild(dayCard);
                }
            }

            getSubjectLabel(subject) {
                const labels = {
                    'math': '📐 Math',
                    'science': '🔬 Sciences',
                    'history': '📚 Histoire',
                    'language': '🗣️ Langues',
                    'other': '📝 Autre'
                };
                return labels[subject] || subject;
            }

            showNotification(message) {
                const notification = document.createElement('div');
                notification.className = 'notification';
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            addDemoData() {
                // Ajouter quelques tâches de démonstration
                setTimeout(() => {
                    this.studyService.addTask({
                        subject: 'math',
                        title: 'Réviser les équations du second degré',
                        description: 'Chapitre 5 - Exercices 1 à 15',
                        priority: 'high',
                        dueDate: new Date().toISOString().split('T')[0],
                        duration: 2
                    });

                    this.studyService.addTask({
                        subject: 'science',
                        title: 'Préparer exposé sur la photosynthèse',
                        description: 'Recherches et création du PowerPoint',
                        priority: 'medium',
                        dueDate: new Date(Date.now() + 86400000).toISOString().split('T')[0],
                        duration: 3
                    });

                    this.studyService.addTask({
                        subject: 'history',
                        title: 'Lire chapitre sur la Révolution française',
                        description: 'Pages 45 à 67 + prendre des notes',
                        priority: 'low',
                        dueDate: new Date(Date.now() + 172800000).toISOString().split('T')[0],
                        duration: 1.5
                    });

                    // Simuler quelques tâches terminées
                    this.studyService.subjects.math.hours = 8.5;
                    this.studyService.subjects.math.progress = 65;
                    this.studyService.subjects.science.hours = 6.0;
                    this.studyService.subjects.science.progress = 45;
                    this.studyService.subjects.history.hours = 4.5;
                    this.studyService.subjects.history.progress = 30;

                    this.updateUI();
                }, 1000);
            }
        }

        // Initialisation de l'application
        let app;
        document.addEventListener('DOMContentLoaded', () => {
            app = new StudyController();
        });
    </script>
</body>
</html>
