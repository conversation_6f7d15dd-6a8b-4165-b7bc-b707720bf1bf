<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskManager - Laravel Style Demo</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav {
            display: flex;
            gap: 1rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
            cursor: pointer;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255,255,255,0.1);
        }

        /* Main Content */
        .main {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: #f7fafc;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border-left: 4px solid #667eea;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card:nth-child(2) { border-left-color: #f59e0b; }
        .stat-card:nth-child(3) { border-left-color: #3b82f6; }
        .stat-card:nth-child(4) { border-left-color: #10b981; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1a202c;
        }

        .stat-label {
            color: #718096;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        /* Table */
        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .table th {
            background: #f7fafc;
            font-weight: 600;
            color: #374151;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        /* Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-in-progress {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-completed {
            background: #d1fae5;
            color: #065f46;
        }

        .badge-high {
            background: #fee2e2;
            color: #991b1b;
        }

        .badge-medium {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-low {
            background: #e0e7ff;
            color: #3730a3;
        }

        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Filters */
        .filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }

        /* Footer */
        .footer {
            background: #2d3748;
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        /* Hidden sections */
        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        /* Alert */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav {
                flex-wrap: wrap;
                justify-content: center;
            }

            .filters {
                flex-direction: column;
                align-items: stretch;
            }

            .table {
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-tasks"></i>
                    <span>TaskManager</span>
                </div>
                <nav class="nav">
                    <a href="#" onclick="showSection('dashboard')" class="nav-link active">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                    <a href="#" onclick="showSection('tasks')" class="nav-link">
                        <i class="fas fa-list"></i> Tâches
                    </a>
                    <a href="#" onclick="showSection('create')" class="nav-link">
                        <i class="fas fa-plus"></i> Nouvelle tâche
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">12</div>
                        <div class="stat-label">
                            <i class="fas fa-tasks"></i>
                            Total des tâches
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div class="stat-label">
                            <i class="fas fa-clock"></i>
                            En attente
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value">4</div>
                        <div class="stat-label">
                            <i class="fas fa-spinner"></i>
                            En cours
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-value">3</div>
                        <div class="stat-label">
                            <i class="fas fa-check"></i>
                            Terminées
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-chart-line"></i>
                            Aperçu des tâches récentes
                        </h2>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Statut</th>
                                    <th>Priorité</th>
                                    <th>Échéance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Finaliser le rapport mensuel</strong></td>
                                    <td><span class="badge badge-in-progress">En cours</span></td>
                                    <td><span class="badge badge-high">Élevée</span></td>
                                    <td>15/01/2024</td>
                                </tr>
                                <tr>
                                    <td><strong>Révision du code backend</strong></td>
                                    <td><span class="badge badge-pending">En attente</span></td>
                                    <td><span class="badge badge-medium">Moyenne</span></td>
                                    <td>20/01/2024</td>
                                </tr>
                                <tr>
                                    <td><strong>Mise à jour documentation</strong></td>
                                    <td><span class="badge badge-completed">Terminée</span></td>
                                    <td><span class="badge badge-low">Faible</span></td>
                                    <td>10/01/2024</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Tasks List Section -->
            <div id="tasks" class="section">
                <div class="card">
                    <div class="card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h2 class="card-title">
                                <i class="fas fa-list"></i>
                                Liste des tâches (12)
                            </h2>
                            <a href="#" onclick="showSection('create')" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Nouvelle tâche
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="filters">
                            <select class="form-control" style="width: auto;">
                                <option>Tous les statuts</option>
                                <option>En attente</option>
                                <option>En cours</option>
                                <option>Terminées</option>
                            </select>
                            <input type="text" class="form-control" placeholder="Rechercher une tâche..." style="max-width: 300px;">
                            <button class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                Rechercher
                            </button>
                        </div>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Statut</th>
                                    <th>Priorité</th>
                                    <th>Échéance</th>
                                    <th>Créée le</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="tasksTableBody">
                                <!-- Tasks will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Create Task Section -->
            <div id="create" class="section">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-plus"></i>
                            Créer une nouvelle tâche
                        </h2>
                    </div>
                    <div class="card-body">
                        <form id="createTaskForm">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-heading"></i>
                                    Titre de la tâche *
                                </label>
                                <input type="text" class="form-control" placeholder="Ex: Finaliser le rapport mensuel" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-align-left"></i>
                                    Description
                                </label>
                                <textarea class="form-control" rows="4" placeholder="Décrivez les détails de la tâche..."></textarea>
                            </div>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-flag"></i>
                                        Priorité
                                    </label>
                                    <select class="form-control">
                                        <option value="low">🟢 Faible</option>
                                        <option value="medium" selected>🟡 Moyenne</option>
                                        <option value="high">🔴 Élevée</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar"></i>
                                        Date d'échéance
                                    </label>
                                    <input type="date" class="form-control">
                                </div>
                            </div>

                            <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Créer la tâche
                                </button>
                                <button type="button" onclick="showSection('tasks')" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 TaskManager - Mini projet Laravel Style (Demo)</p>
        </div>
    </footer>

    <script>
        // Demo data
        const tasks = [
            {
                id: 1,
                title: 'Finaliser le rapport mensuel',
                description: 'Compiler les données de performance et créer le rapport pour la direction',
                status: 'in_progress',
                priority: 'high',
                due_date: '2024-01-15',
                created_at: '2024-01-10 09:00:00'
            },
            {
                id: 2,
                title: 'Révision du code backend',
                description: 'Examiner et optimiser les performances de l\'API',
                status: 'pending',
                priority: 'medium',
                due_date: '2024-01-20',
                created_at: '2024-01-11 14:30:00'
            },
            {
                id: 3,
                title: 'Mise à jour documentation',
                description: 'Mettre à jour la documentation technique du projet',
                status: 'completed',
                priority: 'low',
                due_date: '2024-01-10',
                created_at: '2024-01-08 11:15:00'
            },
            {
                id: 4,
                title: 'Tests unitaires frontend',
                description: 'Écrire les tests pour les nouveaux composants React',
                status: 'pending',
                priority: 'high',
                due_date: '2024-01-18',
                created_at: '2024-01-12 16:45:00'
            },
            {
                id: 5,
                title: 'Optimisation base de données',
                description: 'Analyser et optimiser les requêtes lentes',
                status: 'in_progress',
                priority: 'medium',
                due_date: '2024-01-25',
                created_at: '2024-01-13 10:20:00'
            }
        ];

        // Navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName).classList.add('active');
            
            // Add active class to clicked nav link
            event.target.closest('.nav-link').classList.add('active');
            
            // Populate tasks table if tasks section is shown
            if (sectionName === 'tasks') {
                populateTasksTable();
            }
        }

        // Populate tasks table
        function populateTasksTable() {
            const tbody = document.getElementById('tasksTableBody');
            tbody.innerHTML = '';
            
            tasks.forEach(task => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div>
                            <strong>${task.title}</strong>
                            <div style="font-size: 0.875rem; color: #718096; margin-top: 0.25rem;">
                                ${task.description.substring(0, 60)}...
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge badge-${task.status}">
                            ${getStatusLabel(task.status)}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-${task.priority}">
                            ${getPriorityLabel(task.priority)}
                        </span>
                    </td>
                    <td>${formatDate(task.due_date)}</td>
                    <td>${formatDateTime(task.created_at)}</td>
                    <td>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-secondary btn-sm" onclick="editTask(${task.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteTask(${task.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Helper functions
        function getStatusLabel(status) {
            const labels = {
                'pending': 'En attente',
                'in_progress': 'En cours',
                'completed': 'Terminée'
            };
            return labels[status] || status;
        }

        function getPriorityLabel(priority) {
            const labels = {
                'low': 'Faible',
                'medium': 'Moyenne',
                'high': 'Élevée'
            };
            return labels[priority] || priority;
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('fr-FR');
        }

        function formatDateTime(dateString) {
            return new Date(dateString).toLocaleDateString('fr-FR') + ' ' + 
                   new Date(dateString).toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        }

        // Task actions
        function editTask(id) {
            alert(`Édition de la tâche #${id} (Demo)`);
        }

        function deleteTask(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {
                alert(`Tâche #${id} supprimée (Demo)`);
            }
        }

        // Form submission
        document.getElementById('createTaskForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Tâche créée avec succès ! (Demo)');
            showSection('tasks');
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            populateTasksTable();
        });
    </script>
</body>
</html>
