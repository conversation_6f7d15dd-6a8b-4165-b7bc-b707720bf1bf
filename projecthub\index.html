<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectHub - Gestion de Projets</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #2d3748;
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header professionnel */
        .header {
            background: #1e3a8a;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo i {
            color: #60a5fa;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
        }

        /* Layout principal */
        .main-layout {
            flex: 1;
            display: grid;
            grid-template-columns: 280px 1fr;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            gap: 2rem;
            padding: 2rem;
        }

        /* Sidebar */
        .sidebar {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            height: fit-content;
            border: 1px solid #e2e8f0;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 1rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            color: #475569;
            text-decoration: none;
            transition: all 0.2s;
            margin-bottom: 4px;
            cursor: pointer;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #1e3a8a;
        }

        .nav-item.active {
            background: #1e3a8a;
            color: white;
        }

        .nav-item i {
            width: 20px;
            text-align: center;
        }

        /* Zone de contenu principal */
        .content-area {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .content-header {
            background: #f8fafc;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .content-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .content-body {
            padding: 2rem;
        }

        /* Cards de statistiques */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.2s;
        }

        .stat-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .stat-icon.blue { background: #1e3a8a; }
        .stat-icon.green { background: #059669; }
        .stat-icon.orange { background: #d97706; }
        .stat-icon.purple { background: #7c3aed; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-trend {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .stat-trend.up {
            background: #dcfce7;
            color: #166534;
        }

        .stat-trend.down {
            background: #fef2f2;
            color: #dc2626;
        }

        /* Table professionnelle */
        .data-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .table-header {
            background: #f8fafc;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-weight: 600;
            color: #1e293b;
        }

        .table-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #1e3a8a;
            color: white;
        }

        .btn-primary:hover {
            background: #1e40af;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }

        th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }

        td {
            color: #4b5563;
        }

        tr:hover {
            background: #f9fafb;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .priority-high {
            color: #dc2626;
            font-weight: 600;
        }

        .priority-medium {
            color: #d97706;
            font-weight: 600;
        }

        .priority-low {
            color: #059669;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .sidebar {
                order: 2;
            }

            .content-area {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .content-body {
                padding: 1rem;
            }
        }

        /* Interactions professionnelles */
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        tr:hover {
            background: #f9fafb !important;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #1e3a8a;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* Transitions fluides */
        .stat-card, .btn, .nav-item, tr {
            transition: all 0.2s ease;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Composant principal
        function ProjectHub() {
            const [activeSection, setActiveSection] = useState('dashboard');
            const [projects, setProjects] = useState([]);
            const [stats, setStats] = useState({
                totalProjects: 0,
                activeProjects: 0,
                completedTasks: 0,
                teamMembers: 0
            });
            const [selectedProject, setSelectedProject] = useState(null);
            const [filterStatus, setFilterStatus] = useState('all');

            useEffect(() => {
                // Données de démonstration réalistes
                const demoProjects = [
                    {
                        id: 1,
                        name: 'Refonte Site Web Corporate',
                        status: 'active',
                        priority: 'high',
                        progress: 75,
                        dueDate: '2024-01-15',
                        team: 'Frontend Team',
                        description: 'Modernisation complète du site web avec nouveau design responsive'
                    },
                    {
                        id: 2,
                        name: 'Application Mobile CRM',
                        status: 'pending',
                        priority: 'medium',
                        progress: 45,
                        dueDate: '2024-02-20',
                        team: 'Mobile Team',
                        description: 'Développement application mobile pour gestion client'
                    },
                    {
                        id: 3,
                        name: 'Migration Base de Données',
                        status: 'completed',
                        priority: 'high',
                        progress: 100,
                        dueDate: '2023-12-30',
                        team: 'Backend Team',
                        description: 'Migration vers PostgreSQL et optimisation performances'
                    },
                    {
                        id: 4,
                        name: 'Système de Reporting BI',
                        status: 'active',
                        priority: 'medium',
                        progress: 30,
                        dueDate: '2024-03-10',
                        team: 'Analytics Team',
                        description: 'Mise en place tableaux de bord business intelligence'
                    },
                    {
                        id: 5,
                        name: 'API Gateway Microservices',
                        status: 'pending',
                        priority: 'low',
                        progress: 10,
                        dueDate: '2024-04-15',
                        team: 'Backend Team',
                        description: 'Implémentation architecture microservices avec API Gateway'
                    }
                ];

                setProjects(demoProjects);
                setStats({
                    totalProjects: demoProjects.length,
                    activeProjects: demoProjects.filter(p => p.status === 'active').length,
                    completedTasks: 127,
                    teamMembers: 24
                });
            }, []);

            const filteredProjects = projects.filter(project => {
                if (filterStatus === 'all') return true;
                return project.status === filterStatus;
            });

            return (
                <div className="app-container">
                    <Header />
                    <div className="main-layout">
                        <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />
                        <div className="content-area">
                            <ContentHeader activeSection={activeSection} />
                            <div className="content-body">
                                {activeSection === 'dashboard' && (
                                    <Dashboard
                                        stats={stats}
                                        projects={filteredProjects}
                                        setSelectedProject={setSelectedProject}
                                    />
                                )}
                                {activeSection === 'projects' && (
                                    <ProjectsList
                                        projects={filteredProjects}
                                        setSelectedProject={setSelectedProject}
                                        filterStatus={filterStatus}
                                        setFilterStatus={setFilterStatus}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                    {selectedProject && (
                        <ProjectModal
                            project={selectedProject}
                            onClose={() => setSelectedProject(null)}
                        />
                    )}
                </div>
            );
        }

        // Composant Header
        function Header() {
            return (
                <header className="header">
                    <div className="header-content">
                        <div className="logo">
                            <i className="fas fa-project-diagram"></i>
                            <span>ProjectHub</span>
                        </div>
                        <div className="header-actions">
                            <button className="btn btn-secondary">
                                <i className="fas fa-search"></i>
                            </button>
                            <button className="btn btn-secondary">
                                <i className="fas fa-bell"></i>
                            </button>
                            <div className="user-profile">
                                <i className="fas fa-user-circle"></i>
                                <span>Marie Dubois</span>
                            </div>
                        </div>
                    </div>
                </header>
            );
        }

        // Composant Sidebar
        function Sidebar({ activeSection, setActiveSection }) {
            const navItems = [
                { id: 'dashboard', icon: 'fas fa-chart-line', label: 'Tableau de bord' },
                { id: 'projects', icon: 'fas fa-folder-open', label: 'Projets' }
            ];

            return (
                <aside className="sidebar">
                    <div className="sidebar-section">
                        <div className="sidebar-title">Navigation</div>
                        {navItems.map(item => (
                            <div
                                key={item.id}
                                className={`nav-item ${activeSection === item.id ? 'active' : ''}`}
                                onClick={() => setActiveSection(item.id)}
                            >
                                <i className={item.icon}></i>
                                <span>{item.label}</span>
                            </div>
                        ))}
                    </div>
                    
                    <div className="sidebar-section">
                        <div className="sidebar-title">Projets récents</div>
                        <div className="nav-item">
                            <i className="fas fa-circle" style={{color: '#059669', fontSize: '8px'}}></i>
                            <span>Site Web</span>
                        </div>
                        <div className="nav-item">
                            <i className="fas fa-circle" style={{color: '#d97706', fontSize: '8px'}}></i>
                            <span>App Mobile</span>
                        </div>
                    </div>
                </aside>
            );
        }

        // Composant Content Header
        function ContentHeader({ activeSection }) {
            const titles = {
                dashboard: 'Tableau de bord',
                projects: 'Gestion des projets'
            };

            const icons = {
                dashboard: 'fas fa-chart-line',
                projects: 'fas fa-folder-open'
            };

            return (
                <div className="content-header">
                    <h1 className="content-title">
                        <i className={icons[activeSection] || 'fas fa-chart-line'}></i>
                        {titles[activeSection] || 'ProjectHub'}
                    </h1>
                </div>
            );
        }

        // Composant Dashboard
        function Dashboard({ stats, projects, setSelectedProject }) {
            return (
                <div>
                    <div className="stats-grid">
                        <div className="stat-card" onClick={() => setSelectedProject(null)}>
                            <div className="stat-header">
                                <div className="stat-icon blue">
                                    <i className="fas fa-folder"></i>
                                </div>
                                <div className="stat-trend up">+12%</div>
                            </div>
                            <div className="stat-value">{stats.totalProjects}</div>
                            <div className="stat-label">Projets totaux</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-header">
                                <div className="stat-icon green">
                                    <i className="fas fa-play"></i>
                                </div>
                                <div className="stat-trend up">+8%</div>
                            </div>
                            <div className="stat-value">{stats.activeProjects}</div>
                            <div className="stat-label">Projets actifs</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-header">
                                <div className="stat-icon orange">
                                    <i className="fas fa-check"></i>
                                </div>
                                <div className="stat-trend up">+15%</div>
                            </div>
                            <div className="stat-value">{stats.completedTasks}</div>
                            <div className="stat-label">Tâches terminées</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-header">
                                <div className="stat-icon purple">
                                    <i className="fas fa-users"></i>
                                </div>
                                <div className="stat-trend up">+3%</div>
                            </div>
                            <div className="stat-value">{stats.teamMembers}</div>
                            <div className="stat-label">Membres d'équipe</div>
                        </div>
                    </div>

                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                        gap: '1.5rem',
                        marginBottom: '2rem'
                    }}>
                        <div className="data-table">
                            <div className="table-header">
                                <h3 className="table-title">Activité récente</h3>
                            </div>
                            <div style={{ padding: '1rem' }}>
                                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', padding: '0.5rem', borderRadius: '6px', background: '#f8fafc' }}>
                                        <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#059669' }}></div>
                                        <span style={{ fontSize: '0.875rem', color: '#374151' }}>Migration Base de Données terminée</span>
                                        <span style={{ fontSize: '0.75rem', color: '#64748b', marginLeft: 'auto' }}>Il y a 2h</span>
                                    </div>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', padding: '0.5rem', borderRadius: '6px', background: '#f8fafc' }}>
                                        <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#1e3a8a' }}></div>
                                        <span style={{ fontSize: '0.875rem', color: '#374151' }}>Refonte Site Web mise à jour</span>
                                        <span style={{ fontSize: '0.75rem', color: '#64748b', marginLeft: 'auto' }}>Il y a 4h</span>
                                    </div>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', padding: '0.5rem', borderRadius: '6px', background: '#f8fafc' }}>
                                        <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#dc2626' }}></div>
                                        <span style={{ fontSize: '0.875rem', color: '#374151' }}>Nouveau projet API Gateway créé</span>
                                        <span style={{ fontSize: '0.75rem', color: '#64748b', marginLeft: 'auto' }}>Hier</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="data-table">
                            <div className="table-header">
                                <h3 className="table-title">Échéances à venir</h3>
                            </div>
                            <div style={{ padding: '1rem' }}>
                                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.75rem', border: '1px solid #e2e8f0', borderRadius: '6px' }}>
                                        <div>
                                            <div style={{ fontWeight: '500', color: '#374151', fontSize: '0.875rem' }}>Refonte Site Web</div>
                                            <div style={{ fontSize: '0.75rem', color: '#64748b' }}>Frontend Team</div>
                                        </div>
                                        <div style={{ textAlign: 'right' }}>
                                            <div style={{ fontSize: '0.75rem', color: '#dc2626', fontWeight: '500' }}>15 Jan 2024</div>
                                            <div style={{ fontSize: '0.75rem', color: '#64748b' }}>Dans 3 jours</div>
                                        </div>
                                    </div>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0.75rem', border: '1px solid #e2e8f0', borderRadius: '6px' }}>
                                        <div>
                                            <div style={{ fontWeight: '500', color: '#374151', fontSize: '0.875rem' }}>Application Mobile</div>
                                            <div style={{ fontSize: '0.75rem', color: '#64748b' }}>Mobile Team</div>
                                        </div>
                                        <div style={{ textAlign: 'right' }}>
                                            <div style={{ fontSize: '0.75rem', color: '#f59e0b', fontWeight: '500' }}>20 Fév 2024</div>
                                            <div style={{ fontSize: '0.75rem', color: '#64748b' }}>Dans 1 mois</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <ProjectsTable
                        projects={projects.slice(0, 3)}
                        setSelectedProject={setSelectedProject}
                        showActions={false}
                    />
                </div>
            );
        }

        // Composant Table des projets
        function ProjectsTable({ projects, setSelectedProject, showActions = true }) {
            const getStatusBadge = (status) => {
                const statusMap = {
                    active: { class: 'status-active', text: 'Actif' },
                    pending: { class: 'status-pending', text: 'En attente' },
                    completed: { class: 'status-completed', text: 'Terminé' }
                };
                const statusInfo = statusMap[status] || statusMap.active;
                return <span className={`status-badge ${statusInfo.class}`}>{statusInfo.text}</span>;
            };

            const getPriorityClass = (priority) => {
                return `priority-${priority}`;
            };

            return (
                <div className="data-table">
                    <div className="table-header">
                        <h3 className="table-title">
                            {showActions ? 'Tous les projets' : 'Projets récents'}
                        </h3>
                        {showActions && (
                            <div className="table-actions">
                                <button className="btn btn-secondary">
                                    <i className="fas fa-download"></i>
                                    Exporter
                                </button>
                                <button className="btn btn-primary">
                                    <i className="fas fa-plus"></i>
                                    Nouveau projet
                                </button>
                            </div>
                        )}
                    </div>
                    <div className="table-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>Nom du projet</th>
                                    <th>Statut</th>
                                    <th>Priorité</th>
                                    <th>Progression</th>
                                    <th>Échéance</th>
                                    <th>Équipe</th>
                                    {showActions && <th>Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {projects.map(project => (
                                    <tr
                                        key={project.id}
                                        onClick={() => setSelectedProject(project)}
                                        style={{ cursor: 'pointer' }}
                                    >
                                        <td>
                                            <strong>{project.name}</strong>
                                        </td>
                                        <td>{getStatusBadge(project.status)}</td>
                                        <td>
                                            <span className={getPriorityClass(project.priority)}>
                                                {project.priority === 'high' ? 'Élevée' :
                                                 project.priority === 'medium' ? 'Moyenne' : 'Faible'}
                                            </span>
                                        </td>
                                        <td>
                                            <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                                                <div style={{
                                                    width: '60px',
                                                    height: '6px',
                                                    background: '#e2e8f0',
                                                    borderRadius: '3px',
                                                    overflow: 'hidden'
                                                }}>
                                                    <div style={{
                                                        width: `${project.progress}%`,
                                                        height: '100%',
                                                        background: project.progress === 100 ? '#059669' : '#1e3a8a',
                                                        borderRadius: '3px'
                                                    }}></div>
                                                </div>
                                                <span style={{fontSize: '0.875rem', color: '#64748b'}}>
                                                    {project.progress}%
                                                </span>
                                            </div>
                                        </td>
                                        <td>{new Date(project.dueDate).toLocaleDateString('fr-FR')}</td>
                                        <td>{project.team}</td>
                                        {showActions && (
                                            <td onClick={(e) => e.stopPropagation()}>
                                                <div style={{display: 'flex', gap: '4px'}}>
                                                    <button
                                                        className="btn btn-secondary"
                                                        style={{padding: '4px 8px'}}
                                                        onClick={() => setSelectedProject(project)}
                                                        title="Voir détails"
                                                    >
                                                        <i className="fas fa-eye"></i>
                                                    </button>
                                                    <button
                                                        className="btn btn-secondary"
                                                        style={{padding: '4px 8px'}}
                                                        title="Modifier"
                                                    >
                                                        <i className="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            );
        }

        // Modal de détails du projet
        function ProjectModal({ project, onClose }) {
            return (
                <div style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'rgba(0, 0, 0, 0.5)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1000
                }}>
                    <div style={{
                        background: 'white',
                        borderRadius: '12px',
                        padding: '2rem',
                        maxWidth: '600px',
                        width: '90%',
                        maxHeight: '80vh',
                        overflow: 'auto'
                    }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
                            <h2 style={{ color: '#1e293b', margin: 0 }}>{project.name}</h2>
                            <button
                                onClick={onClose}
                                style={{
                                    background: 'none',
                                    border: 'none',
                                    fontSize: '1.5rem',
                                    cursor: 'pointer',
                                    color: '#64748b'
                                }}
                            >
                                ×
                            </button>
                        </div>

                        <div style={{ marginBottom: '1.5rem' }}>
                            <h3 style={{ color: '#374151', marginBottom: '0.5rem' }}>Description</h3>
                            <p style={{ color: '#64748b', lineHeight: '1.6' }}>{project.description}</p>
                        </div>

                        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginBottom: '1.5rem' }}>
                            <div>
                                <h4 style={{ color: '#374151', marginBottom: '0.5rem' }}>Statut</h4>
                                <span className={`status-badge status-${project.status}`}>
                                    {project.status === 'active' ? 'Actif' :
                                     project.status === 'pending' ? 'En attente' : 'Terminé'}
                                </span>
                            </div>
                            <div>
                                <h4 style={{ color: '#374151', marginBottom: '0.5rem' }}>Priorité</h4>
                                <span className={`priority-${project.priority}`}>
                                    {project.priority === 'high' ? 'Élevée' :
                                     project.priority === 'medium' ? 'Moyenne' : 'Faible'}
                                </span>
                            </div>
                            <div>
                                <h4 style={{ color: '#374151', marginBottom: '0.5rem' }}>Équipe</h4>
                                <p style={{ color: '#64748b', margin: 0 }}>{project.team}</p>
                            </div>
                            <div>
                                <h4 style={{ color: '#374151', marginBottom: '0.5rem' }}>Échéance</h4>
                                <p style={{ color: '#64748b', margin: 0 }}>
                                    {new Date(project.dueDate).toLocaleDateString('fr-FR')}
                                </p>
                            </div>
                        </div>

                        <div style={{ marginBottom: '1.5rem' }}>
                            <h4 style={{ color: '#374151', marginBottom: '0.5rem' }}>Progression</h4>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                <div style={{
                                    flex: 1,
                                    height: '8px',
                                    background: '#e2e8f0',
                                    borderRadius: '4px',
                                    overflow: 'hidden'
                                }}>
                                    <div style={{
                                        width: `${project.progress}%`,
                                        height: '100%',
                                        background: project.progress === 100 ? '#059669' : '#1e3a8a',
                                        borderRadius: '4px'
                                    }}></div>
                                </div>
                                <span style={{ fontWeight: '600', color: '#374151' }}>{project.progress}%</span>
                            </div>
                        </div>

                        <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
                            <button className="btn btn-secondary" onClick={onClose}>
                                Fermer
                            </button>
                            <button className="btn btn-primary">
                                <i className="fas fa-edit"></i>
                                Modifier
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        // Composants pour les autres sections
        function ProjectsList({ projects, setSelectedProject, filterStatus, setFilterStatus }) {
            const projectsByStatus = {
                active: projects.filter(p => p.status === 'active').length,
                pending: projects.filter(p => p.status === 'pending').length,
                completed: projects.filter(p => p.status === 'completed').length
            };

            return (
                <div>
                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                        gap: '1rem',
                        marginBottom: '2rem'
                    }}>
                        <div style={{
                            padding: '1rem',
                            background: '#f8fafc',
                            borderRadius: '8px',
                            border: '1px solid #e2e8f0',
                            cursor: 'pointer'
                        }} onClick={() => setFilterStatus('active')}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                                <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#059669' }}></div>
                                <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Projets actifs</span>
                            </div>
                            <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#1e293b' }}>{projectsByStatus.active}</div>
                        </div>

                        <div style={{
                            padding: '1rem',
                            background: '#f8fafc',
                            borderRadius: '8px',
                            border: '1px solid #e2e8f0',
                            cursor: 'pointer'
                        }} onClick={() => setFilterStatus('pending')}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                                <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#f59e0b' }}></div>
                                <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>En attente</span>
                            </div>
                            <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#1e293b' }}>{projectsByStatus.pending}</div>
                        </div>

                        <div style={{
                            padding: '1rem',
                            background: '#f8fafc',
                            borderRadius: '8px',
                            border: '1px solid #e2e8f0',
                            cursor: 'pointer'
                        }} onClick={() => setFilterStatus('completed')}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                                <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#64748b' }}></div>
                                <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Terminés</span>
                            </div>
                            <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#1e293b' }}>{projectsByStatus.completed}</div>
                        </div>

                        <div style={{
                            padding: '1rem',
                            background: '#f8fafc',
                            borderRadius: '8px',
                            border: '1px solid #e2e8f0',
                            cursor: 'pointer'
                        }} onClick={() => setFilterStatus('all')}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                                <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#1e3a8a' }}></div>
                                <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Tous les projets</span>
                            </div>
                            <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#1e293b' }}>{projects.length}</div>
                        </div>
                    </div>

                    <div style={{ marginBottom: '1.5rem', display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'space-between' }}>
                        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                            <span style={{ color: '#374151', fontWeight: '500' }}>Filtrer:</span>
                            <select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value)}
                                style={{
                                    padding: '6px 12px',
                                    border: '1px solid #e2e8f0',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem'
                                }}
                            >
                                <option value="all">Tous les projets</option>
                                <option value="active">Projets actifs</option>
                                <option value="pending">En attente</option>
                                <option value="completed">Terminés</option>
                            </select>
                        </div>
                        <div style={{ color: '#64748b', fontSize: '0.875rem' }}>
                            {projects.length} projet{projects.length > 1 ? 's' : ''} affiché{projects.length > 1 ? 's' : ''}
                        </div>
                    </div>

                    <ProjectsTable
                        projects={projects}
                        setSelectedProject={setSelectedProject}
                        showActions={true}
                    />
                </div>
            );
        }



        // Rendu de l'application
        ReactDOM.render(<ProjectHub />, document.getElementById('root'));
    </script>
</body>
</html>
