<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProjectHub - Gestion de Projets</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #2d3748;
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header professionnel */
        .header {
            background: #1e3a8a;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo i {
            color: #60a5fa;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
        }

        /* Layout principal */
        .main-layout {
            flex: 1;
            display: grid;
            grid-template-columns: 280px 1fr;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            gap: 2rem;
            padding: 2rem;
        }

        /* Sidebar */
        .sidebar {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            height: fit-content;
            border: 1px solid #e2e8f0;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 1rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            color: #475569;
            text-decoration: none;
            transition: all 0.2s;
            margin-bottom: 4px;
            cursor: pointer;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #1e3a8a;
        }

        .nav-item.active {
            background: #1e3a8a;
            color: white;
        }

        .nav-item i {
            width: 20px;
            text-align: center;
        }

        /* Zone de contenu principal */
        .content-area {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .content-header {
            background: #f8fafc;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .content-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .content-body {
            padding: 2rem;
        }

        /* Cards de statistiques */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.2s;
        }

        .stat-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .stat-icon.blue { background: #1e3a8a; }
        .stat-icon.green { background: #059669; }
        .stat-icon.orange { background: #d97706; }
        .stat-icon.purple { background: #7c3aed; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-trend {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .stat-trend.up {
            background: #dcfce7;
            color: #166534;
        }

        .stat-trend.down {
            background: #fef2f2;
            color: #dc2626;
        }

        /* Table professionnelle */
        .data-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .table-header {
            background: #f8fafc;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-weight: 600;
            color: #1e293b;
        }

        .table-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #1e3a8a;
            color: white;
        }

        .btn-primary:hover {
            background: #1e40af;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }

        th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }

        td {
            color: #4b5563;
        }

        tr:hover {
            background: #f9fafb;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .priority-high {
            color: #dc2626;
            font-weight: 600;
        }

        .priority-medium {
            color: #d97706;
            font-weight: 600;
        }

        .priority-low {
            color: #059669;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .sidebar {
                order: 2;
            }

            .content-area {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .content-body {
                padding: 1rem;
            }
        }

        /* Animations */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .stat-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        .stat-value {
            transition: all 0.3s ease;
        }

        .progress-bar {
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Composant principal
        function ProjectHub() {
            const [activeSection, setActiveSection] = useState('dashboard');
            const [projects, setProjects] = useState([]);
            const [stats, setStats] = useState({
                totalProjects: 0,
                activeProjects: 0,
                completedTasks: 0,
                teamMembers: 0
            });
            const [notifications, setNotifications] = useState([]);
            const [isCreatingProject, setIsCreatingProject] = useState(false);
            const [newProject, setNewProject] = useState({
                name: '',
                priority: 'medium',
                team: '',
                dueDate: ''
            });

            useEffect(() => {
                // Données de démonstration
                const demoProjects = [
                    {
                        id: 1,
                        name: 'Refonte Site Web',
                        status: 'active',
                        priority: 'high',
                        progress: 75,
                        dueDate: '2024-01-15',
                        team: 'Frontend Team'
                    },
                    {
                        id: 2,
                        name: 'Application Mobile',
                        status: 'pending',
                        priority: 'medium',
                        progress: 45,
                        dueDate: '2024-02-20',
                        team: 'Mobile Team'
                    },
                    {
                        id: 3,
                        name: 'Migration Base de Données',
                        status: 'completed',
                        priority: 'high',
                        progress: 100,
                        dueDate: '2023-12-30',
                        team: 'Backend Team'
                    },
                    {
                        id: 4,
                        name: 'Système de Reporting',
                        status: 'active',
                        priority: 'low',
                        progress: 30,
                        dueDate: '2024-03-10',
                        team: 'Analytics Team'
                    }
                ];

                setProjects(demoProjects);
                setStats({
                    totalProjects: demoProjects.length,
                    activeProjects: demoProjects.filter(p => p.status === 'active').length,
                    completedTasks: 127,
                    teamMembers: 24
                });

                // Animation des statistiques au chargement
                setTimeout(() => {
                    animateStats();
                }, 500);

                // Simulation de notifications en temps réel
                const notificationInterval = setInterval(() => {
                    addRandomNotification();
                }, 8000);

                return () => clearInterval(notificationInterval);
            }, []);

            const animateStats = () => {
                // Animation progressive des chiffres
                const statElements = document.querySelectorAll('.stat-value');
                statElements.forEach((element, index) => {
                    const finalValue = parseInt(element.textContent);
                    let currentValue = 0;
                    const increment = finalValue / 30;

                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            element.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            element.textContent = Math.floor(currentValue);
                        }
                    }, 50);
                });
            };

            const addRandomNotification = () => {
                const messages = [
                    '📊 Nouveau rapport généré',
                    '✅ Tâche terminée par l\'équipe',
                    '🚀 Projet mis à jour',
                    '👥 Nouveau membre ajouté',
                    '📅 Échéance approchante'
                ];

                const newNotif = {
                    id: Date.now(),
                    message: messages[Math.floor(Math.random() * messages.length)]
                };

                setNotifications(prev => [newNotif, ...prev.slice(0, 2)]);

                // Supprimer après 5 secondes
                setTimeout(() => {
                    setNotifications(prev => prev.filter(n => n.id !== newNotif.id));
                }, 5000);
            };

            const createProject = () => {
                if (newProject.name && newProject.team && newProject.dueDate) {
                    const project = {
                        id: Date.now(),
                        name: newProject.name,
                        status: 'active',
                        priority: newProject.priority,
                        progress: 0,
                        dueDate: newProject.dueDate,
                        team: newProject.team
                    };

                    setProjects(prev => [project, ...prev]);
                    setStats(prev => ({
                        ...prev,
                        totalProjects: prev.totalProjects + 1,
                        activeProjects: prev.activeProjects + 1
                    }));

                    setNewProject({ name: '', priority: 'medium', team: '', dueDate: '' });
                    setIsCreatingProject(false);

                    addRandomNotification();
                }
            };

            const updateProjectProgress = (projectId) => {
                setProjects(prev => prev.map(project => {
                    if (project.id === projectId) {
                        const newProgress = Math.min(100, project.progress + 15);
                        return { ...project, progress: newProgress };
                    }
                    return project;
                }));
                addRandomNotification();
            };

            const toggleProjectStatus = (projectId) => {
                setProjects(prev => prev.map(project => {
                    if (project.id === projectId) {
                        const newStatus = project.status === 'active' ? 'completed' : 'active';
                        return { ...project, status: newStatus };
                    }
                    return project;
                }));
                addRandomNotification();
            };

            return (
                <div className="app-container">
                    <Header notifications={notifications} />
                    <div className="main-layout">
                        <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />
                        <div className="content-area">
                            <ContentHeader
                                activeSection={activeSection}
                                isCreatingProject={isCreatingProject}
                                setIsCreatingProject={setIsCreatingProject}
                            />
                            <div className="content-body">
                                {activeSection === 'dashboard' && (
                                    <Dashboard
                                        stats={stats}
                                        projects={projects}
                                        updateProjectProgress={updateProjectProgress}
                                        toggleProjectStatus={toggleProjectStatus}
                                    />
                                )}
                                {activeSection === 'projects' && (
                                    <ProjectsList
                                        projects={projects}
                                        updateProjectProgress={updateProjectProgress}
                                        toggleProjectStatus={toggleProjectStatus}
                                        isCreatingProject={isCreatingProject}
                                        newProject={newProject}
                                        setNewProject={setNewProject}
                                        createProject={createProject}
                                        setIsCreatingProject={setIsCreatingProject}
                                    />
                                )}
                                {activeSection === 'tasks' && <TasksView />}
                                {activeSection === 'team' && <TeamView />}
                            </div>
                        </div>
                    </div>
                    <NotificationContainer notifications={notifications} />
                </div>
            );
        }

        // Composant Header
        function Header({ notifications }) {
            const [showNotifications, setShowNotifications] = useState(false);

            return (
                <header className="header">
                    <div className="header-content">
                        <div className="logo">
                            <i className="fas fa-project-diagram"></i>
                            <span>ProjectHub</span>
                        </div>
                        <div className="header-actions">
                            <button
                                className="btn btn-secondary"
                                onClick={() => setShowNotifications(!showNotifications)}
                                style={{ position: 'relative' }}
                            >
                                <i className="fas fa-bell"></i>
                                {notifications.length > 0 && (
                                    <span style={{
                                        position: 'absolute',
                                        top: '-5px',
                                        right: '-5px',
                                        background: '#dc2626',
                                        color: 'white',
                                        borderRadius: '50%',
                                        width: '18px',
                                        height: '18px',
                                        fontSize: '10px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        {notifications.length}
                                    </span>
                                )}
                            </button>
                            <div className="user-profile">
                                <i className="fas fa-user-circle"></i>
                                <span>Marie Dubois</span>
                            </div>
                        </div>
                    </div>
                </header>
            );
        }

        // Composant Sidebar
        function Sidebar({ activeSection, setActiveSection }) {
            const navItems = [
                { id: 'dashboard', icon: 'fas fa-chart-line', label: 'Tableau de bord' },
                { id: 'projects', icon: 'fas fa-folder-open', label: 'Projets' },
                { id: 'tasks', icon: 'fas fa-tasks', label: 'Tâches' },
                { id: 'team', icon: 'fas fa-users', label: 'Équipe' },
                { id: 'calendar', icon: 'fas fa-calendar', label: 'Calendrier' },
                { id: 'reports', icon: 'fas fa-chart-bar', label: 'Rapports' }
            ];

            return (
                <aside className="sidebar">
                    <div className="sidebar-section">
                        <div className="sidebar-title">Navigation</div>
                        {navItems.map(item => (
                            <div
                                key={item.id}
                                className={`nav-item ${activeSection === item.id ? 'active' : ''}`}
                                onClick={() => setActiveSection(item.id)}
                            >
                                <i className={item.icon}></i>
                                <span>{item.label}</span>
                            </div>
                        ))}
                    </div>
                    
                    <div className="sidebar-section">
                        <div className="sidebar-title">Projets récents</div>
                        <div className="nav-item">
                            <i className="fas fa-circle" style={{color: '#059669', fontSize: '8px'}}></i>
                            <span>Site Web</span>
                        </div>
                        <div className="nav-item">
                            <i className="fas fa-circle" style={{color: '#d97706', fontSize: '8px'}}></i>
                            <span>App Mobile</span>
                        </div>
                    </div>
                </aside>
            );
        }

        // Composant Content Header
        function ContentHeader({ activeSection, isCreatingProject, setIsCreatingProject }) {
            const titles = {
                dashboard: 'Tableau de bord',
                projects: 'Gestion des projets',
                tasks: 'Gestion des tâches',
                team: 'Gestion d\'équipe'
            };

            return (
                <div className="content-header">
                    <h1 className="content-title">
                        <i className="fas fa-chart-line"></i>
                        {titles[activeSection] || 'ProjectHub'}
                    </h1>
                    {activeSection === 'projects' && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setIsCreatingProject(!isCreatingProject)}
                        >
                            <i className="fas fa-plus"></i>
                            {isCreatingProject ? 'Annuler' : 'Nouveau projet'}
                        </button>
                    )}
                </div>
            );
        }

        // Composant Dashboard
        function Dashboard({ stats, projects, updateProjectProgress, toggleProjectStatus }) {
            const [animatedStats, setAnimatedStats] = useState(stats);

            useEffect(() => {
                setAnimatedStats(stats);
            }, [stats]);

            return (
                <div>
                    <div className="stats-grid">
                        <div className="stat-card" onClick={() => setAnimatedStats(prev => ({...prev, totalProjects: prev.totalProjects + 1}))}>
                            <div className="stat-header">
                                <div className="stat-icon blue">
                                    <i className="fas fa-folder"></i>
                                </div>
                                <div className="stat-trend up">+12%</div>
                            </div>
                            <div className="stat-value">{animatedStats.totalProjects}</div>
                            <div className="stat-label">Projets totaux</div>
                        </div>

                        <div className="stat-card" onClick={() => setAnimatedStats(prev => ({...prev, activeProjects: prev.activeProjects + 1}))}>
                            <div className="stat-header">
                                <div className="stat-icon green">
                                    <i className="fas fa-play"></i>
                                </div>
                                <div className="stat-trend up">+8%</div>
                            </div>
                            <div className="stat-value">{animatedStats.activeProjects}</div>
                            <div className="stat-label">Projets actifs</div>
                        </div>

                        <div className="stat-card" onClick={() => setAnimatedStats(prev => ({...prev, completedTasks: prev.completedTasks + 5}))}>
                            <div className="stat-header">
                                <div className="stat-icon orange">
                                    <i className="fas fa-check"></i>
                                </div>
                                <div className="stat-trend up">+15%</div>
                            </div>
                            <div className="stat-value">{animatedStats.completedTasks}</div>
                            <div className="stat-label">Tâches terminées</div>
                        </div>

                        <div className="stat-card" onClick={() => setAnimatedStats(prev => ({...prev, teamMembers: prev.teamMembers + 1}))}>
                            <div className="stat-header">
                                <div className="stat-icon purple">
                                    <i className="fas fa-users"></i>
                                </div>
                                <div className="stat-trend up">+3%</div>
                            </div>
                            <div className="stat-value">{animatedStats.teamMembers}</div>
                            <div className="stat-label">Membres d'équipe</div>
                        </div>
                    </div>

                    <ProjectsTable
                        projects={projects}
                        updateProjectProgress={updateProjectProgress}
                        toggleProjectStatus={toggleProjectStatus}
                    />
                </div>
            );
        }

        // Composant Table des projets
        function ProjectsTable({ projects, updateProjectProgress, toggleProjectStatus }) {
            const getStatusBadge = (status, onClick) => {
                const statusMap = {
                    active: { class: 'status-active', text: 'Actif' },
                    pending: { class: 'status-pending', text: 'En attente' },
                    completed: { class: 'status-completed', text: 'Terminé' }
                };
                const statusInfo = statusMap[status] || statusMap.active;
                return (
                    <span
                        className={`status-badge ${statusInfo.class}`}
                        onClick={onClick}
                        style={{ cursor: 'pointer' }}
                    >
                        {statusInfo.text}
                    </span>
                );
            };

            const getPriorityClass = (priority) => {
                return `priority-${priority}`;
            };

            return (
                <div className="data-table">
                    <div className="table-header">
                        <h3 className="table-title">Projets récents</h3>
                        <div className="table-actions">
                            <button className="btn btn-secondary">
                                <i className="fas fa-filter"></i>
                                Filtrer
                            </button>
                            <button className="btn btn-primary">
                                <i className="fas fa-plus"></i>
                                Nouveau projet
                            </button>
                        </div>
                    </div>
                    <div className="table-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>Nom du projet</th>
                                    <th>Statut</th>
                                    <th>Priorité</th>
                                    <th>Progression</th>
                                    <th>Échéance</th>
                                    <th>Équipe</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {projects.map(project => (
                                    <tr key={project.id}>
                                        <td>
                                            <strong>{project.name}</strong>
                                        </td>
                                        <td>{getStatusBadge(project.status, () => toggleProjectStatus(project.id))}</td>
                                        <td>
                                            <span className={getPriorityClass(project.priority)}>
                                                {project.priority === 'high' ? 'Élevée' :
                                                 project.priority === 'medium' ? 'Moyenne' : 'Faible'}
                                            </span>
                                        </td>
                                        <td>
                                            <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                                                <div
                                                    style={{
                                                        width: '60px',
                                                        height: '6px',
                                                        background: '#e2e8f0',
                                                        borderRadius: '3px',
                                                        overflow: 'hidden',
                                                        cursor: 'pointer'
                                                    }}
                                                    onClick={() => updateProjectProgress(project.id)}
                                                    title="Cliquer pour augmenter"
                                                >
                                                    <div style={{
                                                        width: `${project.progress}%`,
                                                        height: '100%',
                                                        background: project.progress === 100 ? '#059669' : '#1e3a8a',
                                                        borderRadius: '3px',
                                                        transition: 'all 0.3s ease'
                                                    }}></div>
                                                </div>
                                                <span style={{fontSize: '0.875rem', color: '#64748b'}}>
                                                    {project.progress}%
                                                </span>
                                            </div>
                                        </td>
                                        <td>{new Date(project.dueDate).toLocaleDateString('fr-FR')}</td>
                                        <td>{project.team}</td>
                                        <td>
                                            <div style={{display: 'flex', gap: '4px'}}>
                                                <button
                                                    className="btn btn-secondary"
                                                    style={{padding: '4px 8px'}}
                                                    onClick={() => updateProjectProgress(project.id)}
                                                    title="Mettre à jour progression"
                                                >
                                                    <i className="fas fa-plus"></i>
                                                </button>
                                                <button
                                                    className="btn btn-secondary"
                                                    style={{padding: '4px 8px'}}
                                                    onClick={() => toggleProjectStatus(project.id)}
                                                    title="Changer statut"
                                                >
                                                    <i className="fas fa-sync"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            );
        }

        // Composant de création de projet
        function ProjectCreationForm({ newProject, setNewProject, createProject, setIsCreatingProject }) {
            return (
                <div style={{
                    background: '#f8fafc',
                    border: '1px solid #e2e8f0',
                    borderRadius: '12px',
                    padding: '1.5rem',
                    marginBottom: '2rem'
                }}>
                    <h3 style={{ marginBottom: '1rem', color: '#1e293b' }}>Créer un nouveau projet</h3>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                        <input
                            type="text"
                            placeholder="Nom du projet"
                            value={newProject.name}
                            onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                            style={{
                                padding: '8px 12px',
                                border: '1px solid #e2e8f0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                            }}
                        />
                        <select
                            value={newProject.priority}
                            onChange={(e) => setNewProject(prev => ({ ...prev, priority: e.target.value }))}
                            style={{
                                padding: '8px 12px',
                                border: '1px solid #e2e8f0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                            }}
                        >
                            <option value="low">Priorité faible</option>
                            <option value="medium">Priorité moyenne</option>
                            <option value="high">Priorité élevée</option>
                        </select>
                        <input
                            type="text"
                            placeholder="Équipe assignée"
                            value={newProject.team}
                            onChange={(e) => setNewProject(prev => ({ ...prev, team: e.target.value }))}
                            style={{
                                padding: '8px 12px',
                                border: '1px solid #e2e8f0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                            }}
                        />
                        <input
                            type="date"
                            value={newProject.dueDate}
                            onChange={(e) => setNewProject(prev => ({ ...prev, dueDate: e.target.value }))}
                            style={{
                                padding: '8px 12px',
                                border: '1px solid #e2e8f0',
                                borderRadius: '6px',
                                fontSize: '0.875rem'
                            }}
                        />
                    </div>
                    <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>
                        <button className="btn btn-primary" onClick={createProject}>
                            <i className="fas fa-save"></i>
                            Créer le projet
                        </button>
                        <button className="btn btn-secondary" onClick={() => setIsCreatingProject(false)}>
                            Annuler
                        </button>
                    </div>
                </div>
            );
        }

        // Composant de notifications
        function NotificationContainer({ notifications }) {
            return (
                <div style={{
                    position: 'fixed',
                    top: '100px',
                    right: '20px',
                    zIndex: 1000,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '10px'
                }}>
                    {notifications.map(notification => (
                        <div
                            key={notification.id}
                            style={{
                                background: 'white',
                                border: '1px solid #e2e8f0',
                                borderRadius: '8px',
                                padding: '12px 16px',
                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                                minWidth: '250px',
                                animation: 'slideIn 0.3s ease-out'
                            }}
                        >
                            <div style={{ fontSize: '0.875rem', color: '#374151' }}>
                                {notification.message}
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        // Composants pour les autres sections
        function ProjectsList({ projects, updateProjectProgress, toggleProjectStatus, isCreatingProject, newProject, setNewProject, createProject, setIsCreatingProject }) {
            return (
                <div>
                    {isCreatingProject && (
                        <ProjectCreationForm
                            newProject={newProject}
                            setNewProject={setNewProject}
                            createProject={createProject}
                            setIsCreatingProject={setIsCreatingProject}
                        />
                    )}
                    <ProjectsTable
                        projects={projects}
                        updateProjectProgress={updateProjectProgress}
                        toggleProjectStatus={toggleProjectStatus}
                    />
                </div>
            );
        }

        function TasksView() {
            return (
                <div>
                    <h2>Gestion des tâches</h2>
                    <p style={{color: '#64748b', marginTop: '1rem'}}>
                        Interface de gestion des tâches en cours de développement...
                    </p>
                </div>
            );
        }

        function TeamView() {
            return (
                <div>
                    <h2>Gestion d'équipe</h2>
                    <p style={{color: '#64748b', marginTop: '1rem'}}>
                        Interface de gestion d'équipe en cours de développement...
                    </p>
                </div>
            );
        }

        // Rendu de l'application
        ReactDOM.render(<ProjectHub />, document.getElementById('root'));
    </script>
</body>
</html>
