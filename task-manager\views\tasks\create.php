<?php
$title = 'Créer une nouvelle tâche';
ob_start();
?>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-plus"></i>
            Créer une nouvelle tâche
        </h2>
    </div>
    <div class="card-body">
        <form method="POST" action="/">
            <div class="form-group">
                <label for="title" class="form-label">
                    <i class="fas fa-heading"></i>
                    Titre de la tâche *
                </label>
                <input type="text" 
                       id="title" 
                       name="title" 
                       class="form-control" 
                       placeholder="Ex: Finaliser le rapport mensuel"
                       value="<?= htmlspecialchars(old('title')) ?>"
                       required>
            </div>

            <div class="form-group">
                <label for="description" class="form-label">
                    <i class="fas fa-align-left"></i>
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          class="form-control" 
                          rows="4"
                          placeholder="Décrivez les détails de la tâche..."><?= htmlspecialchars(old('description')) ?></textarea>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div class="form-group">
                    <label for="priority" class="form-label">
                        <i class="fas fa-flag"></i>
                        Priorité
                    </label>
                    <select id="priority" name="priority" class="form-control form-select">
                        <option value="low" <?= old('priority') === 'low' ? 'selected' : '' ?>>
                            🟢 Faible
                        </option>
                        <option value="medium" <?= old('priority', 'medium') === 'medium' ? 'selected' : '' ?>>
                            🟡 Moyenne
                        </option>
                        <option value="high" <?= old('priority') === 'high' ? 'selected' : '' ?>>
                            🔴 Élevée
                        </option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="due_date" class="form-label">
                        <i class="fas fa-calendar"></i>
                        Date d'échéance
                    </label>
                    <input type="date" 
                           id="due_date" 
                           name="due_date" 
                           class="form-control"
                           value="<?= htmlspecialchars(old('due_date')) ?>"
                           min="<?= date('Y-m-d') ?>">
                </div>
            </div>

            <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Créer la tâche
                </button>
                <a href="/" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Annuler
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Tips Card -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-lightbulb"></i>
            Conseils pour une bonne gestion des tâches
        </h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
            <div style="display: flex; align-items: flex-start; gap: 1rem;">
                <div style="background: #e0e7ff; color: #3730a3; padding: 0.75rem; border-radius: 50%; flex-shrink: 0;">
                    <i class="fas fa-target"></i>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.5rem; color: #374151;">Soyez spécifique</h4>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        Utilisez des titres clairs et des descriptions détaillées pour éviter toute confusion.
                    </p>
                </div>
            </div>

            <div style="display: flex; align-items: flex-start; gap: 1rem;">
                <div style="background: #fef3c7; color: #92400e; padding: 0.75rem; border-radius: 50%; flex-shrink: 0;">
                    <i class="fas fa-clock"></i>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.5rem; color: #374151;">Définissez des échéances</h4>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        Les dates d'échéance vous aident à prioriser et à respecter vos engagements.
                    </p>
                </div>
            </div>

            <div style="display: flex; align-items: flex-start; gap: 1rem;">
                <div style="background: #fee2e2; color: #991b1b; padding: 0.75rem; border-radius: 50%; flex-shrink: 0;">
                    <i class="fas fa-flag"></i>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.5rem; color: #374151;">Utilisez les priorités</h4>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        Classez vos tâches par importance pour vous concentrer sur l'essentiel.
                    </p>
                </div>
            </div>

            <div style="display: flex; align-items: flex-start; gap: 1rem;">
                <div style="background: #d1fae5; color: #065f46; padding: 0.75rem; border-radius: 50%; flex-shrink: 0;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.5rem; color: #374151;">Mettez à jour régulièrement</h4>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        Changez le statut de vos tâches pour suivre votre progression efficacement.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
