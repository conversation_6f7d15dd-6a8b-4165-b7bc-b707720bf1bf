<?php
$title = 'Modifier la tâche';
ob_start();
?>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-edit"></i>
            Modifier la tâche
        </h2>
    </div>
    <div class="card-body">
        <form method="POST" action="/tasks/<?= $task['id'] ?>/edit">
            <div class="form-group">
                <label for="title" class="form-label">
                    <i class="fas fa-heading"></i>
                    Titre de la tâche *
                </label>
                <input type="text" 
                       id="title" 
                       name="title" 
                       class="form-control" 
                       placeholder="Ex: Finaliser le rapport mensuel"
                       value="<?= htmlspecialchars($task['title']) ?>"
                       required>
            </div>

            <div class="form-group">
                <label for="description" class="form-label">
                    <i class="fas fa-align-left"></i>
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          class="form-control" 
                          rows="4"
                          placeholder="Décrivez les détails de la tâche..."><?= htmlspecialchars($task['description']) ?></textarea>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div class="form-group">
                    <label for="status" class="form-label">
                        <i class="fas fa-tasks"></i>
                        Statut
                    </label>
                    <select id="status" name="status" class="form-control form-select">
                        <option value="pending" <?= $task['status'] === 'pending' ? 'selected' : '' ?>>
                            🟡 En attente
                        </option>
                        <option value="in_progress" <?= $task['status'] === 'in_progress' ? 'selected' : '' ?>>
                            🔵 En cours
                        </option>
                        <option value="completed" <?= $task['status'] === 'completed' ? 'selected' : '' ?>>
                            🟢 Terminée
                        </option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="priority" class="form-label">
                        <i class="fas fa-flag"></i>
                        Priorité
                    </label>
                    <select id="priority" name="priority" class="form-control form-select">
                        <option value="low" <?= $task['priority'] === 'low' ? 'selected' : '' ?>>
                            🟢 Faible
                        </option>
                        <option value="medium" <?= $task['priority'] === 'medium' ? 'selected' : '' ?>>
                            🟡 Moyenne
                        </option>
                        <option value="high" <?= $task['priority'] === 'high' ? 'selected' : '' ?>>
                            🔴 Élevée
                        </option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="due_date" class="form-label">
                        <i class="fas fa-calendar"></i>
                        Date d'échéance
                    </label>
                    <input type="date" 
                           id="due_date" 
                           name="due_date" 
                           class="form-control"
                           value="<?= htmlspecialchars($task['due_date']) ?>">
                </div>
            </div>

            <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Mettre à jour
                </button>
                <a href="/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Retour à la liste
                </a>
                <a href="/tasks/<?= $task['id'] ?>/delete" 
                   class="btn btn-danger"
                   onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')"
                   style="margin-left: auto;">
                    <i class="fas fa-trash"></i>
                    Supprimer
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Task Info Card -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-info-circle"></i>
            Informations de la tâche
        </h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
            <div>
                <h4 style="color: #374151; margin-bottom: 0.5rem;">
                    <i class="fas fa-calendar-plus"></i>
                    Date de création
                </h4>
                <p style="color: #6b7280; margin: 0;">
                    <?= (new DateTime($task['created_at']))->format('d/m/Y à H:i') ?>
                </p>
            </div>

            <div>
                <h4 style="color: #374151; margin-bottom: 0.5rem;">
                    <i class="fas fa-calendar-check"></i>
                    Dernière modification
                </h4>
                <p style="color: #6b7280; margin: 0;">
                    <?= (new DateTime($task['updated_at']))->format('d/m/Y à H:i') ?>
                </p>
            </div>

            <div>
                <h4 style="color: #374151; margin-bottom: 0.5rem;">
                    <i class="fas fa-hashtag"></i>
                    ID de la tâche
                </h4>
                <p style="color: #6b7280; margin: 0; font-family: monospace;">
                    #<?= $task['id'] ?>
                </p>
            </div>

            <?php if ($task['due_date']): ?>
                <div>
                    <h4 style="color: #374151; margin-bottom: 0.5rem;">
                        <i class="fas fa-hourglass-half"></i>
                        Temps restant
                    </h4>
                    <p style="margin: 0;">
                        <?php
                        $dueDate = new DateTime($task['due_date']);
                        $now = new DateTime();
                        $diff = $now->diff($dueDate);
                        
                        if ($dueDate < $now && $task['status'] !== 'completed') {
                            echo '<span style="color: #dc2626; font-weight: 600;">En retard de ' . $diff->days . ' jour(s)</span>';
                        } elseif ($task['status'] === 'completed') {
                            echo '<span style="color: #059669; font-weight: 600;">Tâche terminée</span>';
                        } else {
                            echo '<span style="color: #374151;">Dans ' . $diff->days . ' jour(s)</span>';
                        }
                        ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
