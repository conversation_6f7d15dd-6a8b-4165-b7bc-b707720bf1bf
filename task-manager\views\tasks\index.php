<?php
$title = 'Gestion des tâches';
ob_start();
?>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value"><?= $stats['total'] ?></div>
        <div class="stat-label">
            <i class="fas fa-tasks"></i>
            Total des tâches
        </div>
    </div>
    
    <div class="stat-card" style="border-left-color: #f59e0b;">
        <div class="stat-value"><?= $stats['pending'] ?></div>
        <div class="stat-label">
            <i class="fas fa-clock"></i>
            En attente
        </div>
    </div>
    
    <div class="stat-card" style="border-left-color: #3b82f6;">
        <div class="stat-value"><?= $stats['in_progress'] ?></div>
        <div class="stat-label">
            <i class="fas fa-spinner"></i>
            En cours
        </div>
    </div>
    
    <div class="stat-card" style="border-left-color: #10b981;">
        <div class="stat-value"><?= $stats['completed'] ?></div>
        <div class="stat-label">
            <i class="fas fa-check"></i>
            Terminées
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-filter"></i>
            Filtres et recherche
        </h2>
    </div>
    <div class="card-body">
        <form method="GET" class="filters">
            <div class="form-group" style="margin-bottom: 0; min-width: 200px;">
                <select name="status" class="form-control form-select" onchange="this.form.submit()">
                    <option value="all" <?= ($filter ?? 'all') === 'all' ? 'selected' : '' ?>>Tous les statuts</option>
                    <option value="pending" <?= ($filter ?? '') === 'pending' ? 'selected' : '' ?>>En attente</option>
                    <option value="in_progress" <?= ($filter ?? '') === 'in_progress' ? 'selected' : '' ?>>En cours</option>
                    <option value="completed" <?= ($filter ?? '') === 'completed' ? 'selected' : '' ?>>Terminées</option>
                </select>
            </div>
            
            <div class="form-group" style="margin-bottom: 0; min-width: 300px;">
                <input type="text" 
                       name="search" 
                       class="form-control" 
                       placeholder="Rechercher une tâche..." 
                       value="<?= htmlspecialchars($search ?? '') ?>">
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
                Rechercher
            </button>
            
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-times"></i>
                Réinitialiser
            </a>
        </form>
    </div>
</div>

<!-- Tasks Table -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 class="card-title">
                <i class="fas fa-list"></i>
                Liste des tâches (<?= count($tasks) ?>)
            </h2>
            <a href="/tasks/create" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Nouvelle tâche
            </a>
        </div>
    </div>
    <div class="card-body" style="padding: 0;">
        <?php if (empty($tasks)): ?>
            <div style="padding: 3rem; text-align: center; color: #718096;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h3>Aucune tâche trouvée</h3>
                <p>Commencez par créer votre première tâche !</p>
                <a href="/tasks/create" class="btn btn-primary" style="margin-top: 1rem;">
                    <i class="fas fa-plus"></i>
                    Créer une tâche
                </a>
            </div>
        <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>Titre</th>
                        <th>Statut</th>
                        <th>Priorité</th>
                        <th>Échéance</th>
                        <th>Créée le</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($tasks as $task): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($task['title']) ?></strong>
                                    <?php if ($task['description']): ?>
                                        <div style="font-size: 0.875rem; color: #718096; margin-top: 0.25rem;">
                                            <?= htmlspecialchars(substr($task['description'], 0, 100)) ?>
                                            <?= strlen($task['description']) > 100 ? '...' : '' ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-<?= $task['status'] ?>">
                                    <?php
                                    $statusLabels = [
                                        'pending' => 'En attente',
                                        'in_progress' => 'En cours',
                                        'completed' => 'Terminée'
                                    ];
                                    echo $statusLabels[$task['status']] ?? $task['status'];
                                    ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?= $task['priority'] ?>">
                                    <?php
                                    $priorityLabels = [
                                        'low' => 'Faible',
                                        'medium' => 'Moyenne',
                                        'high' => 'Élevée'
                                    ];
                                    echo $priorityLabels[$task['priority']] ?? $task['priority'];
                                    ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($task['due_date']): ?>
                                    <?php
                                    $dueDate = new DateTime($task['due_date']);
                                    $now = new DateTime();
                                    $isOverdue = $dueDate < $now && $task['status'] !== 'completed';
                                    ?>
                                    <span style="color: <?= $isOverdue ? '#dc2626' : '#374151' ?>">
                                        <?= $dueDate->format('d/m/Y') ?>
                                        <?php if ($isOverdue): ?>
                                            <i class="fas fa-exclamation-triangle" style="color: #dc2626; margin-left: 0.25rem;"></i>
                                        <?php endif; ?>
                                    </span>
                                <?php else: ?>
                                    <span style="color: #9ca3af;">Non définie</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?= (new DateTime($task['created_at']))->format('d/m/Y H:i') ?>
                            </td>
                            <td>
                                <div style="display: flex; gap: 0.5rem;">
                                    <a href="/tasks/<?= $task['id'] ?>/edit" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/tasks/<?= $task['id'] ?>/delete" 
                                       class="btn btn-danger btn-sm"
                                       onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layout.php';
?>
