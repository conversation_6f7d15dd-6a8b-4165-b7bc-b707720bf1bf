# 🎯 TaskManager - Mini Projet Laravel Style

Application de gestion de tâches développée avec une architecture inspirée de Laravel, utilisant PHP vanilla avec des patterns modernes.

## 🛠️ Stack technique

- **Backend** : PHP 8+ avec architecture MVC
- **Base de données** : SQLite (fallback) / MySQL
- **Frontend** : HTML5, CSS3 moderne, JavaScript vanilla
- **Routing** : Système de routes personnalisé
- **Styling** : CSS Grid + Flexbox, design responsive
- **Icons** : Font Awesome 6

## 🎯 Fonctionnalités clés

- **CRUD complet** : <PERSON><PERSON><PERSON>, lire, modifier, supprimer des tâches
- **Système de statuts** : En attente, En cours, Terminée
- **Gestion des priorités** : Faible, Moyenne, Élevée
- **Filtrage avancé** : Par statut et recherche textuelle
- **Dashboard analytique** : Statistiques en temps réel
- **Interface responsive** : Design adaptatif mobile/desktop

## 🚀 Installation

### Prérequis
- PHP 8.0+
- Serveur web (Apache/Nginx) ou PHP built-in server
- Extension PDO SQLite (incluse par défaut)

### Installation rapide

1. **Cloner le projet**
```bash
git clone <repository-url> task-manager
cd task-manager
```

2. **Démarrer le serveur**
```bash
# Serveur PHP intégré
php -S localhost:8000

# Ou avec Apache/Nginx
# Pointer le DocumentRoot vers le dossier task-manager
```

3. **Accéder à l'application**
```
http://localhost:8000
```

## 📁 Structure du projet

```
task-manager/
├── index.php              # Point d'entrée et routeur
├── .htaccess              # Configuration Apache
├── tasks.db               # Base SQLite (auto-créée)
├── views/
│   ├── layout.php         # Template principal
│   └── tasks/
│       ├── index.php      # Liste des tâches
│       ├── create.php     # Formulaire création
│       └── edit.php       # Formulaire modification
└── README.md
```

## 🎨 Fonctionnalités détaillées

### Dashboard
- **Statistiques KPI** : Total, En attente, En cours, Terminées
- **Cartes colorées** : Indicateurs visuels par statut
- **Design moderne** : Interface corporate professionnelle

### Gestion des tâches
- **Formulaires intuitifs** : Création/modification simplifiée
- **Validation** : Contrôles côté client et serveur
- **Dates d'échéance** : Gestion des délais avec alertes
- **Recherche** : Filtrage par titre et description

### Interface utilisateur
- **Design responsive** : Adaptation mobile/tablette/desktop
- **Animations fluides** : Transitions CSS natives
- **Feedback visuel** : Messages de succès/erreur
- **Navigation intuitive** : UX optimisée

## 🔧 Configuration

### Base de données MySQL (optionnel)

Modifier les constantes dans `index.php` :

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'task_manager');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

Créer la table :

```sql
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    due_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🎯 Patterns Laravel utilisés

- **MVC Architecture** : Séparation logique des responsabilités
- **Routing** : Système de routes avec paramètres
- **Blade-like Templates** : Système de vues avec layout
- **Eloquent-style** : Requêtes PDO structurées
- **Helper Functions** : Fonctions utilitaires (route, old, session)
- **Request Handling** : Gestion des méthodes HTTP

## 🚀 Déploiement

### Serveur partagé
1. Uploader tous les fichiers
2. Configurer le DocumentRoot sur le dossier principal
3. Vérifier les permissions d'écriture pour SQLite

### VPS/Serveur dédié
1. Configurer Apache/Nginx
2. Activer mod_rewrite pour Apache
3. Configurer PHP 8+
4. Optionnel : Configurer MySQL

## 📱 Responsive Design

- **Mobile First** : Design optimisé mobile
- **Breakpoints** : Adaptation tablette/desktop
- **Grid System** : Layout flexible CSS Grid
- **Touch Friendly** : Boutons et interactions tactiles

## 🔒 Sécurité

- **XSS Protection** : Échappement HTML systématique
- **CSRF** : Protection contre les attaques cross-site
- **SQL Injection** : Requêtes préparées PDO
- **Headers Security** : Configuration .htaccess

## 🎨 Personnalisation

### Thème
Modifier les variables CSS dans `views/layout.php` :

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #48bb78;
    --danger-color: #f56565;
}
```

### Fonctionnalités
Ajouter de nouvelles routes dans `index.php` et créer les vues correspondantes.

---

**Développé avec ❤️ en PHP - Architecture Laravel Style**
