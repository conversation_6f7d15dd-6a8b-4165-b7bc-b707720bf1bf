<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitTracker - Votre Coach Personnel</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .app {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 10px;
        }

        .header p {
            text-align: center;
            color: #718096;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .stat-icon.calories { color: #ff6b6b; }
        .stat-icon.steps { color: #4ecdc4; }
        .stat-icon.time { color: #45b7d1; }
        .stat-icon.heart { color: #f093fb; }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .workout-section, .progress-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .workout-list {
            list-style: none;
        }

        .workout-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .workout-item:hover {
            background: #edf2f7;
            transform: translateX(5px);
        }

        .workout-name {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .workout-details {
            color: #718096;
            font-size: 0.9rem;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 20px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #4a5568;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .achievements {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .achievement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .achievement-badge {
            text-align: center;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .achievement-badge:hover {
            background: #edf2f7;
            transform: scale(1.05);
        }

        .achievement-badge.earned {
            background: linear-gradient(135deg, #ffd89b, #19547b);
            color: white;
        }

        .achievement-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .workout-item.completed {
            background: #c6f6d5;
            border-left-color: #38a169;
        }

        .workout-item.working {
            animation: pulse 1s infinite;
            background: #fed7d7;
            border-left-color: #e53e3e;
        }

        .stat-card.updating {
            animation: pulse 0.5s ease;
        }

        .btn.working {
            background: #e53e3e;
            cursor: not-allowed;
        }

        .btn.working:hover {
            transform: none;
            box-shadow: none;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .header h1 {
                font-size: 2rem;
            }

            .notification {
                right: 10px;
                left: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function FitTracker() {
            const [stats, setStats] = useState({
                calories: 1250,
                steps: 8432,
                workoutTime: 45,
                heartRate: 72
            });

            const [workouts, setWorkouts] = useState([
                { name: "Course matinale", duration: "30 min", calories: 320, type: "cardio", completed: false },
                { name: "Musculation", duration: "45 min", calories: 280, type: "strength", completed: true },
                { name: "Yoga", duration: "20 min", calories: 150, type: "flexibility", completed: false },
                { name: "HIIT", duration: "25 min", calories: 400, type: "cardio", completed: false }
            ]);

            const [achievements, setAchievements] = useState([
                { name: "Premier pas", icon: "👟", earned: true },
                { name: "Marathonien", icon: "🏃", earned: false },
                { name: "Force", icon: "💪", earned: true },
                { name: "Régularité", icon: "🔥", earned: true },
                { name: "Champion", icon: "🏆", earned: false }
            ]);

            const [progress, setProgress] = useState({
                daily: 75,
                weekly: 60,
                monthly: 85
            });

            const [isWorkingOut, setIsWorkingOut] = useState(false);
            const [notification, setNotification] = useState('');

            // Animation des stats en temps réel
            useEffect(() => {
                const interval = setInterval(() => {
                    setStats(prev => ({
                        ...prev,
                        steps: prev.steps + Math.floor(Math.random() * 10),
                        heartRate: 70 + Math.floor(Math.random() * 10),
                        calories: prev.calories + Math.floor(Math.random() * 5)
                    }));
                }, 3000);

                return () => clearInterval(interval);
            }, []);

            // Simulation d'entraînement
            const startWorkout = (workoutIndex) => {
                setIsWorkingOut(true);
                setNotification('🏃‍♂️ Entraînement démarré !');

                setTimeout(() => {
                    setWorkouts(prev => prev.map((w, i) =>
                        i === workoutIndex ? { ...w, completed: true } : w
                    ));
                    setStats(prev => ({
                        ...prev,
                        workoutTime: prev.workoutTime + 30,
                        calories: prev.calories + 200
                    }));
                    setProgress(prev => ({
                        ...prev,
                        daily: Math.min(100, prev.daily + 15)
                    }));
                    setIsWorkingOut(false);
                    setNotification('✅ Entraînement terminé ! +200 calories');

                    // Débloquer un achievement
                    if (Math.random() > 0.5) {
                        setAchievements(prev => prev.map(a =>
                            a.name === "Marathonien" ? { ...a, earned: true } : a
                        ));
                        setTimeout(() => {
                            setNotification('🏆 Nouveau badge débloqué : Marathonien !');
                        }, 1000);
                    }
                }, 2000);

                setTimeout(() => setNotification(''), 4000);
            };

            const addNewWorkout = () => {
                const newWorkouts = [
                    { name: "Natation", duration: "40 min", calories: 350, type: "cardio", completed: false },
                    { name: "Pilates", duration: "35 min", calories: 200, type: "flexibility", completed: false },
                    { name: "Cyclisme", duration: "60 min", calories: 500, type: "cardio", completed: false }
                ];
                const randomWorkout = newWorkouts[Math.floor(Math.random() * newWorkouts.length)];
                setWorkouts(prev => [...prev, randomWorkout]);
                setNotification('➕ Nouvel entraînement ajouté !');
                setTimeout(() => setNotification(''), 3000);
            };

            return (
                <div className="app">
                    {notification && (
                        <div className="notification">
                            {notification}
                        </div>
                    )}

                    <header className="header">
                        <h1><i className="fas fa-dumbbell"></i> FitTracker</h1>
                        <p>Votre coach personnel pour une vie plus saine</p>
                    </header>

                    <div className="stats-grid">
                        <div className="stat-card">
                            <div className="stat-icon calories">
                                <i className="fas fa-fire"></i>
                            </div>
                            <div className="stat-number">{stats.calories}</div>
                            <div className="stat-label">Calories brûlées</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon steps">
                                <i className="fas fa-walking"></i>
                            </div>
                            <div className="stat-number">{stats.steps.toLocaleString()}</div>
                            <div className="stat-label">Pas aujourd'hui</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon time">
                                <i className="fas fa-clock"></i>
                            </div>
                            <div className="stat-number">{stats.workoutTime}</div>
                            <div className="stat-label">Minutes d'exercice</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon heart">
                                <i className="fas fa-heartbeat"></i>
                            </div>
                            <div className="stat-number">{stats.heartRate}</div>
                            <div className="stat-label">BPM moyen</div>
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="workout-section">
                            <h2 className="section-title">
                                <i className="fas fa-list"></i>
                                Entraînements du jour
                            </h2>
                            <ul className="workout-list">
                                {workouts.map((workout, index) => (
                                    <li key={index} className={`workout-item ${workout.completed ? 'completed' : ''} ${isWorkingOut ? 'working' : ''}`}>
                                        <div className="workout-name">
                                            {workout.completed && <i className="fas fa-check-circle" style={{color: '#38a169', marginRight: '8px'}}></i>}
                                            {workout.name}
                                        </div>
                                        <div className="workout-details">
                                            {workout.duration} • {workout.calories} cal
                                        </div>
                                        {!workout.completed && (
                                            <button
                                                className={`btn ${isWorkingOut ? 'working' : ''}`}
                                                onClick={() => startWorkout(index)}
                                                disabled={isWorkingOut}
                                                style={{marginTop: '10px', padding: '8px 15px', fontSize: '0.9rem'}}
                                            >
                                                {isWorkingOut ? <i className="fas fa-spinner fa-spin"></i> : <i className="fas fa-play"></i>}
                                                {isWorkingOut ? ' En cours...' : ' Commencer'}
                                            </button>
                                        )}
                                    </li>
                                ))}
                            </ul>
                            <button className="btn" onClick={addNewWorkout}>
                                <i className="fas fa-plus"></i> Ajouter un entraînement
                            </button>
                        </div>

                        <div className="progress-section">
                            <h2 className="section-title">
                                <i className="fas fa-chart-line"></i>
                                Progression
                            </h2>
                            
                            <div>
                                <strong>Objectif quotidien</strong>
                                <div className="progress-bar">
                                    <div className="progress-fill" style={{width: `${progress.daily}%`}}></div>
                                </div>
                                <div className="progress-text">{progress.daily}% complété</div>
                            </div>

                            <div>
                                <strong>Objectif hebdomadaire</strong>
                                <div className="progress-bar">
                                    <div className="progress-fill" style={{width: `${progress.weekly}%`}}></div>
                                </div>
                                <div className="progress-text">{progress.weekly}% complété</div>
                            </div>

                            <div>
                                <strong>Objectif mensuel</strong>
                                <div className="progress-bar">
                                    <div className="progress-fill" style={{width: `${progress.monthly}%`}}></div>
                                </div>
                                <div className="progress-text">{progress.monthly}% complété</div>
                            </div>
                        </div>
                    </div>

                    <div className="achievements">
                        <h2 className="section-title">
                            <i className="fas fa-trophy"></i>
                            Récompenses
                        </h2>
                        <div className="achievement-grid">
                            {achievements.map((achievement, index) => (
                                <div key={index} className={`achievement-badge ${achievement.earned ? 'earned' : ''}`}>
                                    <div className="achievement-icon">{achievement.icon}</div>
                                    <div>{achievement.name}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<FitTracker />, document.getElementById('root'));
    </script>
</body>
</html>
