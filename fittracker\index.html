<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitTracker - Votre Coach Personnel</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .app {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 10px;
        }

        .header p {
            text-align: center;
            color: #718096;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .stat-icon.calories { color: #ff6b6b; }
        .stat-icon.steps { color: #4ecdc4; }
        .stat-icon.time { color: #45b7d1; }
        .stat-icon.heart { color: #f093fb; }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .workout-section, .progress-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .workout-list {
            list-style: none;
        }

        .workout-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .workout-item:hover {
            background: #edf2f7;
            transform: translateX(5px);
        }

        .workout-name {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .workout-details {
            color: #718096;
            font-size: 0.9rem;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 20px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #4a5568;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .achievements {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .achievement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .achievement-badge {
            text-align: center;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .achievement-badge:hover {
            background: #edf2f7;
            transform: scale(1.05);
        }

        .achievement-badge.earned {
            background: linear-gradient(135deg, #ffd89b, #19547b);
            color: white;
        }

        .achievement-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function FitTracker() {
            const [stats, setStats] = useState({
                calories: 1250,
                steps: 8432,
                workoutTime: 45,
                heartRate: 72
            });

            const [workouts] = useState([
                { name: "Course matinale", duration: "30 min", calories: 320, type: "cardio" },
                { name: "Musculation", duration: "45 min", calories: 280, type: "strength" },
                { name: "Yoga", duration: "20 min", calories: 150, type: "flexibility" },
                { name: "HIIT", duration: "25 min", calories: 400, type: "cardio" }
            ]);

            const [achievements] = useState([
                { name: "Premier pas", icon: "👟", earned: true },
                { name: "Marathonien", icon: "🏃", earned: false },
                { name: "Force", icon: "💪", earned: true },
                { name: "Régularité", icon: "🔥", earned: true },
                { name: "Champion", icon: "🏆", earned: false }
            ]);

            const [progress] = useState({
                daily: 75,
                weekly: 60,
                monthly: 85
            });

            return (
                <div className="app">
                    <header className="header">
                        <h1><i className="fas fa-dumbbell"></i> FitTracker</h1>
                        <p>Votre coach personnel pour une vie plus saine</p>
                    </header>

                    <div className="stats-grid">
                        <div className="stat-card">
                            <div className="stat-icon calories">
                                <i className="fas fa-fire"></i>
                            </div>
                            <div className="stat-number">{stats.calories}</div>
                            <div className="stat-label">Calories brûlées</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon steps">
                                <i className="fas fa-walking"></i>
                            </div>
                            <div className="stat-number">{stats.steps.toLocaleString()}</div>
                            <div className="stat-label">Pas aujourd'hui</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon time">
                                <i className="fas fa-clock"></i>
                            </div>
                            <div className="stat-number">{stats.workoutTime}</div>
                            <div className="stat-label">Minutes d'exercice</div>
                        </div>

                        <div className="stat-card">
                            <div className="stat-icon heart">
                                <i className="fas fa-heartbeat"></i>
                            </div>
                            <div className="stat-number">{stats.heartRate}</div>
                            <div className="stat-label">BPM moyen</div>
                        </div>
                    </div>

                    <div className="main-content">
                        <div className="workout-section">
                            <h2 className="section-title">
                                <i className="fas fa-list"></i>
                                Entraînements du jour
                            </h2>
                            <ul className="workout-list">
                                {workouts.map((workout, index) => (
                                    <li key={index} className="workout-item">
                                        <div className="workout-name">{workout.name}</div>
                                        <div className="workout-details">
                                            {workout.duration} • {workout.calories} cal
                                        </div>
                                    </li>
                                ))}
                            </ul>
                            <button className="btn">
                                <i className="fas fa-plus"></i> Ajouter un entraînement
                            </button>
                        </div>

                        <div className="progress-section">
                            <h2 className="section-title">
                                <i className="fas fa-chart-line"></i>
                                Progression
                            </h2>
                            
                            <div>
                                <strong>Objectif quotidien</strong>
                                <div className="progress-bar">
                                    <div className="progress-fill" style={{width: `${progress.daily}%`}}></div>
                                </div>
                                <div className="progress-text">{progress.daily}% complété</div>
                            </div>

                            <div>
                                <strong>Objectif hebdomadaire</strong>
                                <div className="progress-bar">
                                    <div className="progress-fill" style={{width: `${progress.weekly}%`}}></div>
                                </div>
                                <div className="progress-text">{progress.weekly}% complété</div>
                            </div>

                            <div>
                                <strong>Objectif mensuel</strong>
                                <div className="progress-bar">
                                    <div className="progress-fill" style={{width: `${progress.monthly}%`}}></div>
                                </div>
                                <div className="progress-text">{progress.monthly}% complété</div>
                            </div>
                        </div>
                    </div>

                    <div className="achievements">
                        <h2 className="section-title">
                            <i className="fas fa-trophy"></i>
                            Récompenses
                        </h2>
                        <div className="achievement-grid">
                            {achievements.map((achievement, index) => (
                                <div key={index} className={`achievement-badge ${achievement.earned ? 'earned' : ''}`}>
                                    <div className="achievement-icon">{achievement.icon}</div>
                                    <div>{achievement.name}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<FitTracker />, document.getElementById('root'));
    </script>
</body>
</html>
